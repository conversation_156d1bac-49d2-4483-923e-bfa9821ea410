name: Process Recurring Tasks

on:
  schedule:
    # Run every hour at minute 0
    - cron: '0 * * * *'
  workflow_dispatch: # Allow manual trigger

jobs:
  process-recurring-tasks:
    runs-on: ubuntu-latest
    
    steps:
    - name: Process Recurring Tasks
      run: |
        echo "Processing recurring maintenance tasks..."
        
        # Call the Supabase Edge Function
        response=$(curl -s -w "%{http_code}" \
          -X POST \
          -H "Content-Type: application/json" \
          -H "Authorization: Bearer ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}" \
          "${{ secrets.SUPABASE_URL }}/functions/v1/process-recurring-tasks" \
          -d '{}')
        
        http_code="${response: -3}"
        response_body="${response%???}"
        
        echo "HTTP Status: $http_code"
        echo "Response: $response_body"
        
        if [ "$http_code" -ne 200 ]; then
          echo "Error: Failed to process recurring tasks (HTTP $http_code)"
          echo "Response: $response_body"
          exit 1
        fi
        
        echo "Successfully processed recurring tasks"
        
        # Parse and display results
        echo "$response_body" | jq -r '.message // "No message"'
        echo "$response_body" | jq -r '.successful // 0' | xargs -I {} echo "Successful: {}"
        echo "$response_body" | jq -r '.errors // 0' | xargs -I {} echo "Errors: {}"

    - name: Notify on Failure
      if: failure()
      run: |
        echo "Recurring task processing failed. Check the logs above for details."
        # You could add Slack/Discord/email notifications here if needed
