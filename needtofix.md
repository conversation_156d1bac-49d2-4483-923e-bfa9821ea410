# Stayfu App Test Results

This file documents the results of the tests performed on the Stayfu app.

## AddEntry.tsx

- [x] Renders the main heading
- [x] Renders all entry categories
- [x] Shows the form when a category is selected

## AdminDashboard.tsx

- [x] Renders the admin dashboard for super admins

## Auth.tsx

- [x] Renders the login and register tabs
- [x] Shows the login form by default
- [x] Switches to the register form when the register tab is clicked

## CalendarTestPage.tsx

- [x] Renders the calendar test page
- [x] Renders the basic calendar
- [x] Selects a date when a day is clicked

## CollectionDetail.tsx

- [x] Renders the collection ID from the URL

## Collections.tsx

- [x] Renders the collections page with loading state

