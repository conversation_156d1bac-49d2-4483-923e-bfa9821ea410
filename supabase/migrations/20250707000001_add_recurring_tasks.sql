-- Add recurring task functionality to maintenance_tasks table
-- This migration adds columns to support recurring maintenance tasks

-- Add columns for recurring task functionality
ALTER TABLE maintenance_tasks 
ADD COLUMN IF NOT EXISTS is_recurring boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS recurrence_interval_days integer DEFAULT NULL,
ADD COLUMN IF NOT EXISTS parent_task_id uuid REFERENCES maintenance_tasks(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS next_due_date timestamptz DEFAULT NULL,
ADD COLUMN IF NOT EXISTS recurrence_count integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS max_recurrences integer DEFAULT NULL,
ADD COLUMN IF NOT EXISTS completed_at timestamptz DEFAULT NULL;

-- Create index for efficient querying of recurring tasks
CREATE INDEX IF NOT EXISTS idx_maintenance_tasks_recurring ON maintenance_tasks(is_recurring) WHERE is_recurring = true;
CREATE INDEX IF NOT EXISTS idx_maintenance_tasks_next_due ON maintenance_tasks(next_due_date) WHERE next_due_date IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_maintenance_tasks_parent ON maintenance_tasks(parent_task_id) WHERE parent_task_id IS NOT NULL;

-- Create a function to generate the next recurring task
CREATE OR REPLACE FUNCTION create_next_recurring_task(completed_task_id uuid)
RETURNS uuid AS $$
DECLARE
    task_record maintenance_tasks%ROWTYPE;
    new_task_id uuid;
    new_due_date timestamptz;
BEGIN
    -- Get the completed task details
    SELECT * INTO task_record FROM maintenance_tasks WHERE id = completed_task_id;
    
    -- Check if this is a recurring task and hasn't exceeded max recurrences
    IF NOT task_record.is_recurring OR 
       (task_record.max_recurrences IS NOT NULL AND task_record.recurrence_count >= task_record.max_recurrences) THEN
        RETURN NULL;
    END IF;
    
    -- Calculate next due date
    new_due_date := COALESCE(task_record.next_due_date, NOW()) + (task_record.recurrence_interval_days || ' days')::interval;
    
    -- Create the new recurring task
    INSERT INTO maintenance_tasks (
        user_id,
        property_id,
        property_name,
        title,
        description,
        status,
        severity,
        due_date,
        assigned_to,
        provider_id,
        provider_email,
        team_id,
        is_recurring,
        recurrence_interval_days,
        parent_task_id,
        next_due_date,
        recurrence_count,
        max_recurrences
    ) VALUES (
        task_record.user_id,
        task_record.property_id,
        task_record.property_name,
        task_record.title,
        task_record.description,
        'open', -- New recurring task starts as open
        task_record.severity,
        new_due_date::text, -- Convert to text to match existing schema
        task_record.assigned_to,
        task_record.provider_id,
        task_record.provider_email,
        task_record.team_id,
        true,
        task_record.recurrence_interval_days,
        COALESCE(task_record.parent_task_id, completed_task_id), -- Link to original parent or this task
        new_due_date + (task_record.recurrence_interval_days || ' days')::interval, -- Next occurrence after this one
        task_record.recurrence_count + 1,
        task_record.max_recurrences
    ) RETURNING id INTO new_task_id;
    
    RETURN new_task_id;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger function to handle task completion and recurring task creation
CREATE OR REPLACE FUNCTION handle_task_completion()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if task status changed to completed
    IF OLD.status != 'completed' AND NEW.status = 'completed' THEN
        -- Set completed_at timestamp
        NEW.completed_at := NOW();
        
        -- If this is a recurring task, create the next occurrence
        IF NEW.is_recurring THEN
            PERFORM create_next_recurring_task(NEW.id);
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trigger_task_completion ON maintenance_tasks;
CREATE TRIGGER trigger_task_completion
    BEFORE UPDATE ON maintenance_tasks
    FOR EACH ROW
    EXECUTE FUNCTION handle_task_completion();

-- Create a function to get recurring task series information
CREATE OR REPLACE FUNCTION get_recurring_task_series(task_id uuid)
RETURNS TABLE (
    id uuid,
    title text,
    status text,
    due_date text,
    completed_at timestamptz,
    recurrence_count integer,
    is_original boolean
) AS $$
BEGIN
    RETURN QUERY
    WITH RECURSIVE task_series AS (
        -- Find the root task (original recurring task)
        SELECT 
            t.id,
            t.title,
            t.status,
            t.due_date,
            t.completed_at,
            t.recurrence_count,
            t.parent_task_id,
            true as is_original
        FROM maintenance_tasks t
        WHERE t.id = task_id AND t.parent_task_id IS NULL
        
        UNION ALL
        
        SELECT 
            t.id,
            t.title,
            t.status,
            t.due_date,
            t.completed_at,
            t.recurrence_count,
            t.parent_task_id,
            false as is_original
        FROM maintenance_tasks t
        INNER JOIN task_series ts ON t.parent_task_id = ts.id
        
        UNION ALL
        
        -- Also get tasks that reference the current task as parent
        SELECT 
            t.id,
            t.title,
            t.status,
            t.due_date,
            t.completed_at,
            t.recurrence_count,
            t.parent_task_id,
            false as is_original
        FROM maintenance_tasks t
        WHERE t.parent_task_id = task_id
    )
    SELECT 
        ts.id,
        ts.title,
        ts.status,
        ts.due_date,
        ts.completed_at,
        ts.recurrence_count,
        ts.is_original
    FROM task_series ts
    ORDER BY ts.recurrence_count;
END;
$$ LANGUAGE plpgsql;

-- Add comments to document the new columns
COMMENT ON COLUMN maintenance_tasks.is_recurring IS 'Whether this task repeats on a schedule';
COMMENT ON COLUMN maintenance_tasks.recurrence_interval_days IS 'Number of days between recurring task instances';
COMMENT ON COLUMN maintenance_tasks.parent_task_id IS 'Reference to the original recurring task or previous instance';
COMMENT ON COLUMN maintenance_tasks.next_due_date IS 'When the next instance of this recurring task should be created';
COMMENT ON COLUMN maintenance_tasks.recurrence_count IS 'Which occurrence this is in the series (0 = original)';
COMMENT ON COLUMN maintenance_tasks.max_recurrences IS 'Maximum number of times this task should recur (NULL = infinite)';
COMMENT ON COLUMN maintenance_tasks.completed_at IS 'Timestamp when the task was marked as completed';
