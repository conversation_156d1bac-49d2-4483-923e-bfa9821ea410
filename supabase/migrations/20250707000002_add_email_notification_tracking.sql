-- Add email notification tracking to maintenance_tasks table
-- This allows us to track which recurring tasks have had email notifications sent

ALTER TABLE maintenance_tasks 
ADD COLUMN IF NOT EXISTS email_notification_sent boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS email_notification_sent_at timestamptz DEFAULT NULL;

-- Add index for efficient querying of tasks needing email notifications
CREATE INDEX IF NOT EXISTS idx_maintenance_tasks_email_notifications 
ON maintenance_tasks (is_recurring, email_notification_sent, created_at) 
WHERE is_recurring = true AND email_notification_sent = false;

-- Add comment to explain the new columns
COMMENT ON COLUMN maintenance_tasks.email_notification_sent IS 'Tracks whether an email notification has been sent for this task';
COMMENT ON COLUMN maintenance_tasks.email_notification_sent_at IS 'Timestamp when the email notification was sent';

-- Update the create_next_recurring_task function to set email_notification_sent to false for new tasks
CREATE OR REPLACE FUNCTION create_next_recurring_task(completed_task_id uuid)
RETURNS uuid AS $$
DECLARE
    task_record maintenance_tasks%ROWTYPE;
    new_task_id uuid;
    new_due_date timestamptz;
BEGIN
    -- Get the completed task details
    SELECT * INTO task_record FROM maintenance_tasks WHERE id = completed_task_id;
    
    -- Check if this is a recurring task and hasn't exceeded max recurrences
    IF NOT task_record.is_recurring OR 
       (task_record.max_recurrences IS NOT NULL AND task_record.recurrence_count >= task_record.max_recurrences) THEN
        RETURN NULL;
    END IF;
    
    -- Calculate next due date
    new_due_date := COALESCE(task_record.next_due_date, NOW()) + (task_record.recurrence_interval_days || ' days')::interval;
    
    -- Create the new recurring task
    INSERT INTO maintenance_tasks (
        user_id,
        property_id,
        property_name,
        title,
        description,
        status,
        severity,
        due_date,
        assigned_to,
        provider_id,
        provider_email,
        team_id,
        is_recurring,
        recurrence_interval_days,
        parent_task_id,
        next_due_date,
        recurrence_count,
        max_recurrences,
        email_notification_sent -- Explicitly set to false for new recurring tasks
    ) VALUES (
        task_record.user_id,
        task_record.property_id,
        task_record.property_name,
        task_record.title,
        task_record.description,
        'open',
        task_record.severity,
        new_due_date::date,
        task_record.assigned_to,
        task_record.provider_id,
        task_record.provider_email,
        task_record.team_id,
        true,
        task_record.recurrence_interval_days,
        COALESCE(task_record.parent_task_id, completed_task_id), -- Link to original parent or this task
        new_due_date + (task_record.recurrence_interval_days || ' days')::interval, -- Next occurrence after this one
        task_record.recurrence_count + 1,
        task_record.max_recurrences,
        false -- New recurring tasks need email notifications
    ) RETURNING id INTO new_task_id;
    
    RETURN new_task_id;
END;
$$ LANGUAGE plpgsql;
