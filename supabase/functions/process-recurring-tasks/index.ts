import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the service role key
    const supabaseAdmin = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    console.log('Starting recurring task email notification processing...');

    // Find recurring tasks that need email notifications
    // These are tasks created by the database trigger but haven't had emails sent yet
    const { data: tasksNeedingEmails, error: fetchError } = await supabaseAdmin
      .from('maintenance_tasks')
      .select('*')
      .eq('is_recurring', true)
      .eq('email_notification_sent', false)
      .not('provider_email', 'is', null) // Only tasks with provider emails
      .in('status', ['open', 'new']) // Only active tasks
      .lte('due_date', new Date().toISOString().split('T')[0]); // Only tasks that are due now or overdue

    if (fetchError) {
      console.error('Error fetching overdue recurring tasks:', fetchError);
      throw fetchError;
    }

    if (!tasksNeedingEmails || tasksNeedingEmails.length === 0) {
      console.log('No recurring tasks need email notifications');
      return new Response(
        JSON.stringify({
          message: 'No recurring tasks need email notifications',
          processed: 0
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    console.log(`Found ${tasksNeedingEmails.length} recurring tasks needing email notifications`);

    const results = [];
    let successCount = 0;
    let errorCount = 0;

    for (const task of tasksNeedingEmails) {
      try {
        console.log(`Sending email notification for recurring task: ${task.id} - ${task.title}`);

        // Send email notification for this recurring task
        try {
          if (task.provider_email) {
            console.log(`Sending recurring task notification to provider: ${task.provider_email}`);

            // Generate accept/decline URLs
            const acceptToken = await generateTaskToken(task.id);
            const acceptUrl = `${Deno.env.get('SITE_URL') || 'https://www.stayfu.com'}/maintenance-response?id=${task.id}&action=accept&token=${acceptToken}`;
            const rejectUrl = `${Deno.env.get('SITE_URL') || 'https://www.stayfu.com'}/maintenance-response?id=${task.id}&action=decline&token=${acceptToken}`;

            // Get property manager email for reply-to
            const { data: userProfile } = await supabaseAdmin
              .from('profiles')
              .select('email')
              .eq('id', task.user_id)
              .single();

            const { error: emailError } = await supabaseAdmin.functions.invoke('send-email', {
              body: {
                from: `StayFu Maintenance <<EMAIL>>`,
                to: task.provider_email,
                subject: `Recurring Maintenance Task: ${task.title}`,
                html: `
                  <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2>🔄 Recurring Maintenance Task: ${task.title}</h2>
                    <p style="background-color: #e3f2fd; padding: 10px; border-radius: 4px; margin: 15px 0;">
                      <strong>📅 This is a recurring task occurrence #${task.recurrence_count}</strong>
                    </p>
                    <p><strong>Property:</strong> ${task.property_name || 'Not specified'}</p>
                    <p><strong>Severity:</strong> ${task.severity || 'Not specified'}</p>
                    <p><strong>Due Date:</strong> ${task.due_date || 'No due date'}</p>
                    <p><strong>Description:</strong> ${task.description || 'No description provided'}</p>

                    <div style="margin: 30px 0; text-align: center;">
                      <a href="${acceptUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; margin-right: 10px;">
                        ✅ Accept Task
                      </a>
                      <a href="${rejectUrl}" style="display: inline-block; background-color: #f44336; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
                        ❌ Decline Task
                      </a>
                    </div>

                    <p style="font-size: 0.9em; color: #666;">
                      This is an automatically generated recurring task. If you have questions, please reply to this email.
                    </p>
                  </div>
                `,
                reply_to: userProfile?.email
              }
            });

            if (emailError) {
              console.error(`Error sending recurring task email for ${task.id}:`, emailError);
              throw emailError;
            } else {
              console.log(`Recurring task notification sent successfully for ${task.id}`);

              // Mark email as sent
              await supabaseAdmin
                .from('maintenance_tasks')
                .update({
                  email_notification_sent: true,
                  email_notification_sent_at: new Date().toISOString()
                })
                .eq('id', task.id);

              successCount++;
              results.push({
                taskId: task.id,
                status: 'email_sent',
                message: `Email notification sent for recurring task occurrence #${task.recurrence_count}`
              });
            }
          } else {
            console.log(`No provider email for task ${task.id}, skipping email notification`);

            // Mark as processed even without email
            await supabaseAdmin
              .from('maintenance_tasks')
              .update({
                email_notification_sent: true,
                email_notification_sent_at: new Date().toISOString()
              })
              .eq('id', task.id);

            results.push({
              taskId: task.id,
              status: 'skipped',
              message: 'No provider email available'
            });
          }
        } catch (emailError) {
          console.error(`Error sending recurring task notification for ${task.id}:`, emailError);
          errorCount++;
          results.push({
            taskId: task.id,
            status: 'error',
            message: `Failed to send email: ${emailError instanceof Error ? emailError.message : 'Unknown error'}`
          });
        }



      } catch (error) {
        console.error(`Error processing recurring task ${task.id}:`, error);
        errorCount++;
        results.push({
          taskId: task.id,
          status: 'error',
          message: `Processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
      }
    }

    const summary = {
      message: `Processed ${tasksNeedingEmails.length} recurring task email notifications`,
      totalProcessed: tasksNeedingEmails.length,
      successful: successCount,
      errors: errorCount,
      results
    };

    console.log('Recurring task processing completed:', summary);

    return new Response(
      JSON.stringify(summary),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error in process-recurring-tasks function:', error);
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

/**
 * Generate a secure token for task response URLs
 */
async function generateTaskToken(taskId: string): Promise<string> {
  try {
    const encoder = new TextEncoder();
    const data = encoder.encode(taskId + "maintenance-response-secret");
    const hashBuffer = await crypto.subtle.digest("SHA-256", data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const token = hashArray.map(b => b.toString(16).padStart(2, "0")).join("").substring(0, 32);
    return token;
  } catch (error) {
    console.error("Error generating task token:", error);
    throw error;
  }
}
