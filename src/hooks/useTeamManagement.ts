import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types/supabase';
import emailService from '@/services/emailService';

export type TeamMember = {
  id: string;
  user_id: string;
  team_id: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  status: string;
  avatar_url?: string | null;
  created_at: string;
  profile_role?: UserRole;
};

export type Team = {
  id: string;
  name: string;
  owner_id: string;
  created_at: string;
  updated_at: string;
  member_count?: number;
};

export const useTeamManagement = () => {
  const { authState } = useAuth();
  const [loading, setLoading] = useState(false);
  const [teams, setTeams] = useState<Team[]>([]);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [teamProperties, setTeamProperties] = useState<string[]>([]);
  const [sentInvitations, setSentInvitations] = useState<any[]>([]);
  const [loadingSentInvitations, setLoadingSentInvitations] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
  const [dataInitialized, setDataInitialized] = useState(false);

  const fetchUserTeams = useCallback(async () => {
    if (!authState.user?.id) return;

    setLoading(true);
    try {
      const { data: ownedTeams, error: ownedError } = await supabase
        .from('teams')
        .select('*, team_members:team_members(count)')
        .eq('owner_id', authState.user.id);

      if (ownedError) throw ownedError;

      const { data: memberTeams, error: memberError } = await supabase
        .from('teams')
        .select('*, team_members:team_members(count)')
        .neq('owner_id', authState.user.id)
        .in('id', await getTeamMemberships(authState.user.id));

      if (memberError) throw memberError;

      const allTeams = [...(ownedTeams || []), ...(memberTeams || [])];
      const teamsWithMemberCount = allTeams.map(team => ({
        ...team,
        member_count: team.team_members?.[0]?.count || 0
      }));

      setTeams(teamsWithMemberCount);

      if (!selectedTeam && teamsWithMemberCount.length > 0) {
        console.log('[useTeamManagement] Auto-selecting first team:', teamsWithMemberCount[0].name, teamsWithMemberCount[0].id);
        setSelectedTeam(teamsWithMemberCount[0]);
      }
    } catch (error: any) {
      console.error('Error fetching teams:', error);
      setTimeout(() => toast.error('Failed to load teams'), 0);
    } finally {
      setLoading(false);
    }
  }, [authState.user?.id]);

  useEffect(() => {
    if (authState.user?.id && !dataInitialized) {
      fetchUserTeams();
      setDataInitialized(true);
    }
  }, [authState.user?.id, dataInitialized, fetchUserTeams]);

  // Track ongoing requests to prevent duplicates
  const ongoingRequests = useRef<Set<string>>(new Set());

  const fetchTeamMembers = useCallback(async (teamId: string) => {
    if (!teamId) return;

    // Prevent duplicate requests for the same team
    const requestKey = `team-members-${teamId}`;
    if (ongoingRequests.current.has(requestKey)) {
      console.log(`[useTeamManagement] Skipping duplicate request for team: ${teamId}`);
      return;
    }

    console.log(`[useTeamManagement] Fetching team members for team: ${teamId}`);
    ongoingRequests.current.add(requestKey);
    setTeamMembers([]);
    setLoading(true);

    try {
      const { data: joinData, error: joinError } = await supabase
        .from('team_members')
        .select(`
          *,
          profiles:user_id (
            id,
            email,
            first_name,
            last_name,
            avatar_url,
            role
          )
        `)
        .eq('team_id', teamId);

      if (joinError) {
        console.error('Error in direct team_members query:', joinError);
        throw joinError;
      }

      const formattedMembers = joinData.map((member: any) => ({
        id: member.id,
        user_id: member.user_id,
        team_id: member.team_id,
        email: member.profiles?.email,
        first_name: member.profiles?.first_name,
        last_name: member.profiles?.last_name,
        status: member.status,
        avatar_url: member.profiles?.avatar_url,
        created_at: member.created_at,
        profile_role: member.profiles?.role
      }));

      setTeamMembers(formattedMembers);
      console.log(`[useTeamManagement] Set ${formattedMembers.length} team members for team ${teamId}:`, formattedMembers.map(m => ({ id: m.user_id, email: m.email })));
    } catch (error: any) {
      console.error('Error fetching team members:', error);

      if (error.message?.includes('JWT') || error.message?.includes('auth') || error.code === 'PGRST301') {
        setTimeout(() => toast.error('Authentication session expired. Please refresh the page.'), 0);
      } else {
        setTimeout(() => toast.error('Failed to load team members'), 0);
      }
      throw error;
    } finally {
      ongoingRequests.current.delete(requestKey);
      setLoading(false);
    }
  }, []);

  const fetchTeamProperties = useCallback(async (teamId: string) => {
    if (!teamId) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .rpc('get_team_properties', {
          p_team_id: teamId
        });

      if (error) {
        console.error('Error fetching team properties from database:', error);
        throw error;
      }

      const teamPropertyIds = data || [];
      setTeamProperties(teamPropertyIds);
    } catch (error: any) {
      console.error('Error fetching team properties:', error);
      setTimeout(() => toast.error('Failed to load team properties'), 0);
    } finally {
      setLoading(false);
    }
  }, []);

  const [initialFetchDone, setInitialFetchDone] = useState(false);

  useEffect(() => {
    if (selectedTeam?.id && !initialFetchDone) {
      setInitialFetchDone(true);

      fetchTeamMembers(selectedTeam.id)
        .catch(err => {
          console.error('Error in initial team members fetch:', err);
        });
    }
  }, [selectedTeam?.id, initialFetchDone, fetchTeamMembers]);

  const getTeamMemberships = async (userId: string): Promise<string[]> => {
    try {
      const { data, error } = await supabase
        .from('team_members')
        .select('team_id')
        .eq('user_id', userId)
        .eq('status', 'active');

      if (error) throw error;

      const teamIds = (data || []).map((item: any) => item.team_id);
      return teamIds;
    } catch (error) {
      console.error('Error getting team memberships:', error);
      return [];
    }
  };

  const ensureUserProfile = useCallback(async () => {
    if (!authState.user?.id) return false;

    try {
      const { data: existingProfile, error: checkError } = await supabase
        .from('profiles')
        .select('id')
        .eq('id', authState.user.id)
        .maybeSingle();

      if (checkError && checkError.code !== 'PGRST116') {
        console.error('Error checking profile:', checkError);
        return false;
      }

      if (!existingProfile) {
        const { error: insertError } = await supabase
          .from('profiles')
          .insert({
            id: authState.user.id,
            email: authState.user.email || authState.profile?.email || '',
            first_name: authState.profile?.first_name || null,
            last_name: authState.profile?.last_name || null,
            role: authState.profile?.role || 'property_manager',
            is_super_admin: Boolean(authState.profile?.is_super_admin)
          });

        if (insertError) {
          console.error('Error creating profile:', insertError);
          return false;
        }
      }

      return true;
    } catch (error: any) {
      console.error('Error in ensureUserProfile:', error);
      return false;
    }
  }, [authState.user?.id, authState.user?.email, authState.profile]);

  const generateInvitationToken = (): string => {
    return crypto.randomUUID();
  };

  const inviteUserToTeam = useCallback(async (teamId: string, email: string, role: UserRole) => {
    if (!authState.user?.id || !teamId) {
      setTimeout(() => toast.error('Unable to send invitation at this time'), 0);
      return false;
    }

    setLoading(true);
    try {
      const invitationToken = generateInvitationToken();

      const { data, error } = await supabase.functions.invoke('create-team-invitation', {
        body: {
          teamId,
          email,
          role: String(role),
          invitedBy: authState.user.id,
          token: invitationToken
        }
      });

      if (error) {
        console.error('Error creating invitation:', {
          error,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        throw error;
      }

      const { data: teamData } = await supabase
        .from('teams')
        .select('name')
        .eq('id', teamId)
        .single();

      const inviterName = `${authState.profile?.first_name || ''} ${authState.profile?.last_name || ''}`.trim() || authState.profile?.email || 'A user';
      const teamName = teamData?.name || 'a team';

      try {
        const emailResult = await emailService.sendTeamInvitationEmail(
          email,
          inviterName,
          teamName,
          role,
          invitationToken
        );

        if (!emailResult.success && emailResult.error) {
          console.error('Failed to send invitation email:', emailResult.error);
          setTimeout(() => toast.warning('Invitation created, but email delivery failed. The person may not receive a notification.'), 0);
        } else {
          if (emailResult.error) {
            setTimeout(() => toast.success('Invitation created successfully. In development mode, emails are not actually sent.'), 0);
          } else {
            setTimeout(() => toast.success('Invitation sent successfully'), 0);
          }
        }
      } catch (emailError) {
        console.error('Error sending invitation email:', emailError);
        setTimeout(() => toast.warning('Invitation created, but there was an issue sending the email notification.'), 0);
      }

      return true;
    } catch (error: any) {
      console.error('Error inviting user to team:', error);
      setTimeout(() => toast.error('Failed to send invitation: ' + error.message), 0);
      return false;
    } finally {
      setLoading(false);
    }
  }, [authState.user?.id, authState.profile]);

  const getInvitationDetails = useCallback(async (token: string) => {
    try {
      const { data, error } = await supabase
        .from('team_invitations')
        .select('*, teams:team_id(name)')
        .eq('token', token)
        .maybeSingle();

      if (error) {
        return {
          exists: false,
          error: error.message || 'Error fetching invitation details',
          invitation: null
        };
      }

      if (!data) {
        return {
          exists: false,
          error: 'Invitation not found',
          invitation: null
        };
      }

      if (data.status !== 'pending') {
        return {
          exists: false,
          error: `Invitation has already been ${data.status}`,
          invitation: null
        };
      }

      if (data.expires_at && new Date(data.expires_at) < new Date()) {
        return {
          exists: false,
          error: 'Invitation has expired',
          invitation: null
        };
      }

      return {
        exists: true,
        error: null,
        invitation: data
      };
    } catch (error: any) {
      return {
        exists: false,
        error: error.message || 'An unexpected error occurred',
        invitation: null
      };
    }
  }, []);

  const acceptTeamInvitation = useCallback(async (token: string) => {
    setLoading(true);
    try {
      if (!authState.user?.id) {
        return {
          success: false,
          error: 'You must be logged in to accept an invitation',
          requiresAuth: true,
          invitation: null
        };
      }

      const profileEnsured = await ensureUserProfile();
      if (!profileEnsured) {
         return {
           success: false,
           error: 'Failed to prepare user profile for team joining.',
           requiresAuth: false,
           invitation: null
         };
      }

      const { data: invitationData, error: invitationError } = await supabase
        .from('team_invitations')
        .select('email, role, team_id, team_name, status')
        .eq('token', token)
        .maybeSingle();

      if (invitationError) {
      } else if (invitationData) {
        if (invitationData.email !== authState.user.email) {
        }

        if (invitationData.team_id) {
          localStorage.setItem('lastAcceptedTeamId', invitationData.team_id);
        }
      }

      const { data: rpcData, error: rpcError } = await supabase.rpc(
        'accept_invitation_and_add_member',
        {
          p_token: token,
          p_user_id: authState.user.id
        }
      );

      if (rpcError) {
        return {
          success: false,
          error: 'Failed to accept invitation: ' + (rpcError.message || 'Database error'),
          requiresAuth: false,
          invitation: null
        };
      }

      if (!rpcData || !rpcData.success) {
        const { data: failedInvite } = await supabase
          .from('team_invitations')
          .select('*, teams:team_id(name)')
          .eq('token', token)
          .maybeSingle();

        return {
          success: false,
          error: rpcData?.error || 'Invitation could not be accepted.',
          requiresAuth: false,
          invitation: failedInvite || null
        };
      }

      const { data: teamData } = await supabase
        .from('teams')
        .select('name')
        .eq('id', rpcData.team_id)
        .single();

      setTimeout(() => toast.success(`You've joined ${teamData?.name || 'the team'}`), 0);

      const { data: finalInvitation } = await supabase
        .from('team_invitations')
        .select('*, teams:team_id(name)')
        .eq('token', token)
        .maybeSingle();

      if (rpcData.team_id) {
        try {
          await fetchTeamMembers(rpcData.team_id);
        } catch (refreshError) {
        }
      }

      localStorage.removeItem('pendingInvitation');
      localStorage.removeItem('pendingInvitationEmail');
      localStorage.removeItem('pendingInvitationTeamId');
      localStorage.removeItem('pendingInvitationRole');
      localStorage.removeItem('pendingInvitationTeamName');

      return {
        success: true,
        error: null,
        requiresAuth: false,
        invitation: finalInvitation,
        teamId: rpcData.team_id
      };
    } catch (error: any) {
      setTimeout(() => toast.error('Failed to accept invitation: ' + error.message), 0);
      return {
        success: false,
        error: error.message,
        requiresAuth: false,
        invitation: null
      };
    } finally {
      setLoading(false);
    }
  }, [authState.user?.id, authState.user?.email, ensureUserProfile, fetchTeamMembers]);

  const createTeam = useCallback(async (name: string) => {
    if (!authState.user?.id) {
      setTimeout(() => toast.error('You must be logged in to create a team'), 0);
      return null;
    }

    setLoading(true);
    try {
      const profileExists = await ensureUserProfile();
      if (!profileExists) {
        setTimeout(() => toast.error('Unable to create your user profile'), 0);
        return null;
      }

      const { data, error } = await supabase
        .from('teams')
        .insert({
          name,
          owner_id: authState.user.id
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      const { error: memberError } = await supabase
        .from('team_members')
        .insert({
          team_id: data.id,
          user_id: authState.user.id,
          added_by: authState.user.id,
          status: 'active'
        });

      if (memberError) {
        setTimeout(() => toast.warning('Team created, but there was an issue adding you as a member.'), 0);
      } else {
        setTimeout(() => toast.success('Team created successfully'), 0);
      }

      await fetchUserTeams();
      return data;
    } catch (error: any) {
      setTimeout(() => toast.error('Failed to create team: ' + error.message), 0);
      return null;
    } finally {
      setLoading(false);
    }
  }, [authState.user?.id, ensureUserProfile, fetchUserTeams]);

  const updateTeam = useCallback(async (teamId: string, name: string) => {
    if (!authState.user?.id) return false;

    setLoading(true);
    try {
      const { error } = await supabase
        .from('teams')
        .update({ name })
        .eq('id', teamId)
        .eq('owner_id', authState.user.id);

      if (error) throw error;

      setTimeout(() => toast.success('Team updated successfully'), 0);
      await fetchUserTeams();
      return true;
    } catch (error: any) {
      setTimeout(() => toast.error('Failed to update team'), 0);
      return false;
    } finally {
      setLoading(false);
    }
  }, [authState.user?.id, fetchUserTeams]);

  const removeTeamMember = useCallback(async (teamMemberId: string) => {
    if (!authState.user?.id) return false;

    setLoading(true);
    try {
      const { data: teamMember, error: fetchError } = await supabase
        .from('team_members')
        .select('team_id, user_id, profiles:user_id(role)')
        .eq('id', teamMemberId)
        .single();

      if (fetchError) throw fetchError;
      if (!teamMember?.team_id) throw new Error('Team member not found');

      if (teamMember.profiles?.role === 'admin' || teamMember.profiles?.role === 'super_admin') {
        const { data: currentUserProfile } = await supabase
          .from('profiles')
          .select('is_super_admin')
          .eq('id', authState.user.id)
          .single();

        if (!currentUserProfile?.is_super_admin) {
          throw new Error('You do not have permission to remove an admin');
        }
      }

      const { error } = await supabase
        .from('team_members')
        .delete()
        .eq('id', teamMemberId);

      if (error) {
        if (error.code === 'PGRST301') {
          throw new Error('You do not have permission to remove this team member');
        }
        throw error;
      }

      await supabase
        .from('user_permissions')
        .delete()
        .eq('user_id', teamMember.user_id)
        .eq('team_id', teamMember.team_id);

      setTimeout(() => toast.success('Team member removed successfully'), 0);
      if (selectedTeam) {
        await fetchTeamMembers(selectedTeam.id);
      }
      return true;
    } catch (error: any) {
      setTimeout(() => toast.error(error.message || 'Failed to remove team member'), 0);
      return false;
    } finally {
      setLoading(false);
    }
  }, [authState.user?.id, selectedTeam, fetchTeamMembers]);

  const assignPropertyToTeam = useCallback(async (teamId: string, propertyId: string) => {
    if (!authState.user?.id || !teamId || !propertyId) {
      setTimeout(() => toast.error('Missing required information'), 0);
      return false;
    }

    setLoading(true);
    try {
      const { data, error } = await supabase.rpc(
        'add_property_to_team_direct',
        {
          p_team_id: teamId,
          p_property_id: propertyId,
          p_user_id: authState.user.id
        }
      );

      if (error) {
        try {
          const { data: propertyData, error: propertyError } = await supabase
            .from('properties')
            .select('user_id')
            .eq('id', propertyId)
            .single();

          if (propertyError) throw propertyError;

          if (propertyData.user_id !== authState.user.id) {
            throw new Error('You can only assign properties you own to a team');
          }

          const { data: teamData, error: teamError } = await supabase
            .from('teams')
            .select('owner_id')
            .eq('id', teamId)
            .single();

          if (teamError) throw teamError;

          if (teamData.owner_id !== authState.user.id) {
            throw new Error('You can only assign properties to teams you own');
          }

          const { error: insertError } = await supabase
            .from('team_properties')
            .insert({
              team_id: teamId,
              property_id: propertyId
            });

          if (insertError) {
            if (insertError.code === '23505') {
            } else {
              throw insertError;
            }
          }
        } catch (fallbackError: any) {
          throw fallbackError;
        }
      } else {
      }

      setTimeout(() => toast.success('Property assigned to team successfully'), 0);

      await fetchTeamProperties(teamId);
      return true;
    } catch (error: any) {
      setTimeout(() => toast.error(error.message || 'Failed to assign property to team'), 0);
      return false;
    } finally {
      setLoading(false);
    }
  }, [authState.user?.id, fetchTeamProperties]);

  const unassignPropertyFromTeam = useCallback(async (teamId: string, propertyId: string) => {
    if (!authState.user?.id || !teamId || !propertyId) {
      setTimeout(() => toast.error('Missing required information'), 0);
      return false;
    }

    setLoading(true);
    try {
      const { data, error } = await supabase.rpc(
        'remove_property_from_team_direct',
        {
          p_team_id: teamId,
          p_property_id: propertyId,
          p_user_id: authState.user.id
        }
      );

      if (error) {
        try {
          const { data: teamData, error: teamError } = await supabase
            .from('teams')
            .select('owner_id')
            .eq('id', teamId)
            .single();

          if (teamError) throw teamError;

          if (teamData.owner_id !== authState.user.id) {
            throw new Error('You can only manage properties for teams you own');
          }

          const { error: deleteError } = await supabase
            .from('team_properties')
            .delete()
            .eq('team_id', teamId)
            .eq('property_id', propertyId);

          if (deleteError) {
            throw deleteError;
          }

          await supabase
            .from('inventory_items')
            .update({ team_id: null })
            .eq('property_id', propertyId)
            .eq('team_id', teamId);

        } catch (fallbackError: any) {
          throw fallbackError;
        }
      } else {
      }

      setTimeout(() => toast.success('Property removed from team successfully'), 0);

      await fetchTeamProperties(teamId);
      return true;
    } catch (error: any) {
      setTimeout(() => toast.error(error.message || 'Failed to remove property from team'), 0);
      return false;
    } finally {
      setLoading(false);
    }
  }, [authState.user?.id, fetchTeamProperties]);

  const deleteTeam = useCallback(async (teamId: string) => {
    if (!authState.user?.id || !teamId) {
      setTimeout(() => toast.error('Missing required information'), 0);
      return false;
    }

    setLoading(true);
    try {
      const { data: teamData, error: teamError } = await supabase
        .from('teams')
        .select('owner_id, name')
        .eq('id', teamId)
        .single();

      if (teamError) throw teamError;

      if (teamData.owner_id !== authState.user.id) {
        throw new Error('You can only delete teams you own');
      }

      const confirmed = window.confirm(`Are you sure you want to delete the team "${teamData.name}"? This action cannot be undone. All team members will lose access to team properties.`);
      if (!confirmed) {
        setLoading(false);
        return false;
      }

      const { error: deleteError } = await supabase.rpc('delete_team_cascade', {
        team_id_param: teamId
      });

      if (deleteError) {
        toast.error(`Failed to delete team: ${deleteError.message}`, { id: 'delete-team' });

        const { error: sqlError } = await supabase.functions.invoke('execute-sql', {
          body: {
            query: `
              DELETE FROM team_members WHERE team_id = '${teamId}';
              DELETE FROM team_properties WHERE team_id = '${teamId}';
              UPDATE inventory_items SET team_id = NULL WHERE team_id = '${teamId}';
              DELETE FROM user_permissions WHERE team_id = '${teamId}';
              DELETE FROM teams WHERE id = '${teamId}';
            `
          }
        });

        if (sqlError) {
          toast.error(`Failed to delete team: ${sqlError.message}`, { id: 'delete-team' });
          return false;
        }
      }

      setTimeout(() => toast.success('Team deleted successfully'), 0);
      await fetchUserTeams();
      return true;
    } catch (error: any) {
      setTimeout(() => toast.error(error.message || 'Failed to delete team'), 0);
      return false;
    } finally {
      setLoading(false);
    }
  }, [authState.user?.id, fetchUserTeams]);

  const fetchSentInvitations = useCallback(async (teamId: string) => {
    if (!authState.user?.id || !teamId) return;

    if (loadingSentInvitations) {
      return;
    }

    setLoadingSentInvitations(true);
    try {
      const { data, error } = await supabase
        .from('team_invitations')
        .select('*')
        .eq('team_id', teamId)
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (error) throw error;

      setSentInvitations(data || []);
    } catch (error: any) {
      setTimeout(() => toast.error('Failed to load sent invitations'), 0);
    } finally {
      setLoadingSentInvitations(false);
    }
  }, [authState.user?.id, loadingSentInvitations]);

  const deleteInvitation = useCallback(async (invitationId: string) => {
    if (!authState.user?.id) {
      setTimeout(() => toast.error('You must be logged in to delete invitations'), 0);
      return false;
    }

    setLoading(true);
    try {
      const { data: invitation, error: fetchError } = await supabase
        .from('team_invitations')
        .select('team_id, invited_by')
        .eq('id', invitationId)
        .single();

      if (fetchError) throw fetchError;

      const { data: team, error: teamError } = await supabase
        .from('teams')
        .select('owner_id')
        .eq('id', invitation.team_id)
        .single();

      if (teamError) throw teamError;

      const isTeamOwner = team.owner_id === authState.user.id;
      const isInviter = invitation.invited_by === authState.user.id;

      if (!isTeamOwner && !isInviter) {
        throw new Error('You do not have permission to delete this invitation');
      }

      const { error } = await supabase
        .from('team_invitations')
        .delete()
        .eq('id', invitationId);

      if (error) throw error;

      await fetchSentInvitations(invitation.team_id);

      setTimeout(() => toast.success('Invitation deleted successfully'), 0);
      return true;
    } catch (error: any) {
      setTimeout(() => toast.error(error.message || 'Failed to delete invitation'), 0);
      return false;
    } finally {
      setLoading(false);
    }
  }, [authState.user?.id, fetchSentInvitations]);

  const resendInvitation = useCallback(async (invitationId: string) => {
    if (!authState.user?.id) {
      setTimeout(() => toast.error('You must be logged in to resend invitations'), 0);
      return false;
    }

    setLoading(true);
    try {
      const { data: invitation, error: fetchError } = await supabase
        .from('team_invitations')
        .select('*, teams:team_id(name)')
        .eq('id', invitationId)
        .single();

      if (fetchError) throw fetchError;

      const { data: team, error: teamError } = await supabase
        .from('teams')
        .select('owner_id, name')
        .eq('id', invitation.team_id)
        .single();

      if (teamError) throw teamError;

      const isTeamOwner = team.owner_id === authState.user.id;
      const isInviter = invitation.invited_by === authState.user.id;

      if (!isTeamOwner && !isInviter) {
        throw new Error('You do not have permission to resend this invitation');
      }

      const { error: updateError } = await supabase
        .from('team_invitations')
        .update({
          expires_at: new Date(Date.now() + (7 * 24 * 60 * 60 * 1000)).toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', invitationId);

      if (updateError) throw updateError;

      try {
        const inviterName = `${authState.profile?.first_name || ''} ${authState.profile?.last_name || ''}`.trim() || authState.profile?.email || 'A user';
        const teamName = team.name || 'a team';

        const emailResult = await emailService.sendTeamInvitationEmail(
          invitation.email,
          inviterName,
          teamName,
          invitation.role,
          invitation.token
        );

        if (!emailResult.success && emailResult.error) {
          setTimeout(() => toast.warning('Invitation updated, but email delivery failed. The person may not receive a notification.'), 0);
        } else {
          setTimeout(() => toast.success('Invitation resent successfully'), 0);
        }
      } catch (emailError) {
        setTimeout(() => toast.warning('Invitation updated, but there was an issue sending the email notification.'), 0);
      }

      await fetchSentInvitations(invitation.team_id);
      return true;
    } catch (error: any) {
      setTimeout(() => toast.error(error.message || 'Failed to resend invitation'), 0);
      return false;
    } finally {
      setLoading(false);
    }
  }, [authState.user?.id, authState.profile, fetchSentInvitations]);

  return {
    loading,
    teams,
    setTeams,
    teamMembers,
    teamProperties,
    sentInvitations,
    loadingSentInvitations,
    selectedTeam,
    setSelectedTeam,
    fetchUserTeams,
    createTeam,
    updateTeam,
    deleteTeam,
    fetchTeamMembers,
    fetchTeamProperties,
    fetchSentInvitations,
    assignPropertyToTeam,
    unassignPropertyFromTeam,
    inviteUserToTeam,
    acceptTeamInvitation,
    getInvitationDetails,
    removeTeamMember,
    deleteInvitation,
    resendInvitation
  };
};