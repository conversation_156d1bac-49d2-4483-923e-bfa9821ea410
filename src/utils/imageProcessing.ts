// Import removed to fix unused import warning

// Add a global type declaration for the bucket name and folder path
declare global {
  interface Window {
    INVENTORY_BUCKET_NAME?: string;
    INVENTORY_FOLDER_PATH?: string;
  }
}

import { supabase } from '@/integrations/supabase/client';

const MAX_IMAGE_SIZE = 1024; // Max width or height in pixels
const JPEG_QUALITY = 0.8; // JPEG compression quality (0.0 - 1.0)

/**
 * Resizes and compresses an image file using HTML Canvas.
 * @param file The image file to process.
 * @param maxWidth The maximum width for the image.
 * @param maxHeight The maximum height for the image.
 * @param quality The JPEG compression quality (0.0 - 1.0).
 * @returns A Promise that resolves with the processed image as a Blob.
 */
async function resizeAndCompressImage(
  file: File,
  maxWidth: number,
  maxHeight: number,
  quality: number
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event) => {
      const img = new Image();
      img.src = event.target?.result as string;
      img.onload = () => {
        const canvas = document.createElement('canvas');
        let width = img.width;
        let height = img.height;

        // Calculate new dimensions to fit within maxWidth/maxHeight while maintaining aspect ratio
        if (width > height) {
          if (width > maxWidth) {
            height *= maxWidth / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width *= maxHeight / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        const ctx = canvas.getContext('2d');
        if (!ctx) {
          return reject(new Error('Could not get canvas context'));
        }
        ctx.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('Canvas toBlob failed'));
            }
          },
          'image/jpeg',
          quality
        );
      };
      img.onerror = (error) => {
        reject(new Error('Image loading failed: ' + error));
      };
    };
    reader.onerror = (error) => {
      reject(new Error('FileReader error: ' + error));
    };
  });
}

/**
 * Downloads an image from a URL, compresses it, and uploads it to Supabase storage
 * If the storage bucket doesn't exist, returns the original URL
 * Handles base64 data URLs by converting them to files and uploading to Supabase
 */
export async function processAndUploadImage(
  imageUrl: string,
  bucketName: string = 'inventory',
  folderPath: string = 'inventory-images',
  fileName?: string
): Promise<string> {
  // Check if the URL is a base64 data URL
  if (imageUrl.startsWith('data:image')) {
    try {
      console.log('Processing base64 data URL');

      // Convert base64 to blob
      const response = await fetch(imageUrl);
      const originalBlob = await response.blob();

      // Create a File object from the blob for resizing
      const originalFile = new File([originalBlob], fileName || 'image.jpg', { type: originalBlob.type });

      // Resize and compress the image
      const processedBlob = await resizeAndCompressImage(
        originalFile,
        MAX_IMAGE_SIZE,
        MAX_IMAGE_SIZE,
        JPEG_QUALITY
      );

      // Generate a unique filename if not provided
      const timestamp = new Date().getTime();
      const randomString = Math.random().toString(36).substring(2, 10);
      const generatedFileName = fileName || `${timestamp}-${randomString}.jpg`;
      const filePath = `${folderPath}/${generatedFileName}`;

      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from(bucketName)
        .upload(filePath, processedBlob, {
          contentType: 'image/jpeg',
          upsert: true
        });

      if (error) {
        console.error('Error uploading processed image to storage:', error);
        return imageUrl; // Return original URL on error
      }

      // Get the public URL
      const { data: publicUrlData } = supabase.storage
        .from(bucketName)
        .getPublicUrl(filePath);

      console.log('Base64 image uploaded successfully:', publicUrlData.publicUrl);
      return publicUrlData.publicUrl;
    } catch (error) {
      console.error('Error processing base64 image:', error);
      return imageUrl; // Return original URL on error
    }
  }

  // For non-base64 URLs, just return the original URL
  return imageUrl;
}

/**
 * Creates inventory buckets if they don't exist
 * Simplified version that just returns true to prevent blocking the dashboard
 */
export async function createInventoryBuckets(): Promise<boolean> {
  try {
    // Set global variables for bucket names
    window.INVENTORY_BUCKET_NAME = 'inventory';
    window.INVENTORY_FOLDER_PATH = 'inventory-images';

    // For dashboard performance, just return true
    return true;
  } catch (error) {
    console.error('Error checking inventory buckets:', error);
    return false;
  }
}