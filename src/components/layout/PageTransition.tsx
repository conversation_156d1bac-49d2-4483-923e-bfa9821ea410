
import React, { ReactNode, useEffect, useState } from 'react';
import { motion } from 'framer-motion';

type PageTransitionProps = {
  children: ReactNode;
};

const pageVariants = {
  initial: {
    opacity: 0,
    y: 10,
    scale: 0.98,
  },
  in: {
    opacity: 1,
    y: 0,
    scale: 1,
  },
  out: {
    opacity: 0,
    y: -10,
    scale: 0.98,
  },
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.3, // Reduced from 0.4 to make transitions faster
};

const PageTransition = ({ children }: PageTransitionProps) => {
  const [isClient, setIsClient] = useState(false);

  // Only enable animations after component mounts to prevent hydration issues
  useEffect(() => {
    setIsClient(true);
  }, []);

  // If not client-side yet, render without animation to prevent flicker
  if (!isClient) {
    return <div>{children}</div>;
  }

  return (
    <motion.div
      initial="initial"
      animate="in"
      exit="out"
      variants={pageVariants}
      transition={pageTransition}
    >
      {children}
    </motion.div>
  );
};

export default PageTransition;
