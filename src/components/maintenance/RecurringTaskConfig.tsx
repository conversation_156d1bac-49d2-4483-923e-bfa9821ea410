import React from 'react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { RecurringTaskConfig, RECURRENCE_INTERVALS, RECURRENCE_LABELS } from '@/components/maintenance/types';
import { formatRecurrenceDescription } from '@/utils/recurringTasks';
import { CalendarDays, Repeat, AlertCircle } from 'lucide-react';

interface RecurringTaskConfigProps {
  config: RecurringTaskConfig;
  onChange: (config: RecurringTaskConfig) => void;
  disabled?: boolean;
}

export const RecurringTaskConfigComponent: React.FC<RecurringTaskConfigProps> = ({
  config,
  onChange,
  disabled = false
}) => {
  const handleRecurringToggle = (isRecurring: boolean) => {
    onChange({
      ...config,
      isRecurring,
      // Set default interval if enabling recurring
      recurrenceIntervalDays: isRecurring && !config.recurrenceIntervalDays 
        ? RECURRENCE_INTERVALS.MONTHLY 
        : config.recurrenceIntervalDays
    });
  };

  const handleIntervalChange = (value: string) => {
    const intervalDays = parseInt(value);
    onChange({
      ...config,
      recurrenceIntervalDays: intervalDays
    });
  };

  const handleCustomIntervalChange = (value: string) => {
    const intervalDays = parseInt(value) || 1;
    onChange({
      ...config,
      recurrenceIntervalDays: intervalDays
    });
  };

  const handleMaxRecurrencesChange = (value: string) => {
    const maxRecurrences = value === '' ? undefined : parseInt(value) || undefined;
    onChange({
      ...config,
      maxRecurrences
    });
  };

  const isCustomInterval = config.recurrenceIntervalDays && 
    !Object.values(RECURRENCE_INTERVALS).includes(config.recurrenceIntervalDays);

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <div className="flex items-center space-x-2">
          <Repeat className="h-5 w-5 text-blue-500" />
          <CardTitle className="text-lg">Recurring Task</CardTitle>
        </div>
        <CardDescription>
          Configure this task to repeat automatically when completed
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6 p-4">
        {/* Enable/Disable Recurring */}
        <div className="flex items-start justify-between gap-4">
          <div className="space-y-1 flex-1">
            <Label htmlFor="recurring-toggle" className="text-sm font-medium">
              Make this task recurring
            </Label>
            <p className="text-xs text-muted-foreground">
              Automatically create a new task when this one is completed
            </p>
          </div>
          <Switch
            id="recurring-toggle"
            checked={config.isRecurring}
            onCheckedChange={handleRecurringToggle}
            disabled={disabled}
            className="flex-shrink-0"
          />
        </div>

        {config.isRecurring && (
          <>
            {/* Recurrence Interval */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Repeat every</Label>
              <Select
                value={isCustomInterval ? 'custom' : config.recurrenceIntervalDays?.toString()}
                onValueChange={handleIntervalChange}
                disabled={disabled}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select interval" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(RECURRENCE_INTERVALS).map(([key, days]) => (
                    <SelectItem key={key} value={days.toString()}>
                      {RECURRENCE_LABELS[days]}
                    </SelectItem>
                  ))}
                  <SelectItem value="custom">Custom interval</SelectItem>
                </SelectContent>
              </Select>

              {isCustomInterval && (
                <div className="flex items-center gap-3">
                  <Input
                    type="number"
                    min="1"
                    max="365"
                    value={config.recurrenceIntervalDays || ''}
                    onChange={(e) => handleCustomIntervalChange(e.target.value)}
                    placeholder="Days"
                    className="w-24"
                    disabled={disabled}
                  />
                  <span className="text-sm text-muted-foreground">days</span>
                </div>
              )}
            </div>

            {/* Max Recurrences */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Maximum occurrences (optional)</Label>
              <div className="flex items-center gap-3">
                <Input
                  type="number"
                  min="1"
                  max="1000"
                  value={config.maxRecurrences || ''}
                  onChange={(e) => handleMaxRecurrencesChange(e.target.value)}
                  placeholder="Unlimited"
                  className="w-36"
                  disabled={disabled}
                />
                <span className="text-sm text-muted-foreground">
                  Leave empty for unlimited
                </span>
              </div>
            </div>

            {/* Preview */}
            {config.recurrenceIntervalDays && (
              <div className="rounded-lg bg-blue-50 p-4 border border-blue-200">
                <div className="flex items-start space-x-2">
                  <CalendarDays className="h-4 w-4 text-blue-500 mt-0.5" />
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-blue-900">
                      Recurrence Schedule
                    </p>
                    <p className="text-sm text-blue-700">
                      {formatRecurrenceDescription(
                        config.recurrenceIntervalDays,
                        config.maxRecurrences
                      )}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Warning for short intervals */}
            {config.recurrenceIntervalDays && config.recurrenceIntervalDays < 7 && (
              <div className="rounded-lg bg-amber-50 p-4 border border-amber-200">
                <div className="flex items-start space-x-2">
                  <AlertCircle className="h-4 w-4 text-amber-500 mt-0.5" />
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-amber-900">
                      Frequent Recurrence
                    </p>
                    <p className="text-sm text-amber-700">
                      This task will repeat very frequently. Make sure this is intended.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default RecurringTaskConfigComponent;
