// Add these types if they're not already defined

export type MaintenanceStatus =
  | 'new'
  | 'assigned'
  | 'in_progress'
  | 'completed'
  | 'cancelled'
  | 'accepted'
  | 'rejected'
  | 'open'
  | 'pending';

export type MaintenanceSeverity = 'low' | 'medium' | 'high' | 'critical';

export interface MaintenanceTask {
  id: string;
  title: string;
  description: string;
  propertyId?: string;
  propertyName: string;
  status: MaintenanceStatus;
  severity: MaintenanceSeverity;
  dueDate: string;
  assignedTo?: string;
  createdAt: string;
  providerId?: string;
  providerEmail?: string;
  userId?: string; // The user who created this task
  // Recurring task properties
  isRecurring?: boolean;
  recurrenceIntervalDays?: number;
  parentTaskId?: string;
  nextDueDate?: string;
  recurrenceCount?: number;
  maxRecurrences?: number;
  completedAt?: string;
}

export interface Provider {
  id: string;
  name: string;
  email: string;
  phone: string;
  specialty: string;
  address: string;
  user_id?: string; // The user who created this provider
}

// Define and export MaintenanceTaskFilters
export interface MaintenanceTaskFilters {
  status?: MaintenanceStatus;
  severity?: MaintenanceSeverity;
  propertyId?: string;
  providerId?: string;
  showCompleted?: boolean;
  showRecurring?: boolean;
  // Add other potential filter fields as needed
}

// Recurring task configuration interface
export interface RecurringTaskConfig {
  isRecurring: boolean;
  recurrenceIntervalDays: number;
  maxRecurrences?: number; // undefined means infinite
}

// Recurring task series information
export interface RecurringTaskSeries {
  id: string;
  title: string;
  status: MaintenanceStatus;
  dueDate: string;
  completedAt?: string;
  recurrenceCount: number;
  isOriginal: boolean;
}

// Common recurrence intervals for UI
export const RECURRENCE_INTERVALS = {
  DAILY: 1,
  WEEKLY: 7,
  BIWEEKLY: 14,
  MONTHLY: 30,
  QUARTERLY: 90,
  SEMIANNUALLY: 180,
  ANNUALLY: 365,
} as const;

export const RECURRENCE_LABELS = {
  [RECURRENCE_INTERVALS.DAILY]: 'Daily',
  [RECURRENCE_INTERVALS.WEEKLY]: 'Weekly',
  [RECURRENCE_INTERVALS.BIWEEKLY]: 'Every 2 weeks',
  [RECURRENCE_INTERVALS.MONTHLY]: 'Monthly',
  [RECURRENCE_INTERVALS.QUARTERLY]: 'Quarterly',
  [RECURRENCE_INTERVALS.SEMIANNUALLY]: 'Every 6 months',
  [RECURRENCE_INTERVALS.ANNUALLY]: 'Annually',
} as const;
