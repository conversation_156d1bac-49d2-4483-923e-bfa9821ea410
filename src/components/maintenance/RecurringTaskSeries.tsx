import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { RecurringTaskSeries } from '@/components/maintenance/types';
import { getRecurringTaskSeries } from '@/utils/recurringTasks';
import { format } from 'date-fns';
import { 
  History, 
  CheckCircle2, 
  Clock, 
  Calendar, 
  ChevronDown, 
  ChevronUp,
  Repeat
} from 'lucide-react';

interface RecurringTaskSeriesProps {
  taskId: string;
  isRecurring: boolean;
  onTaskSelect?: (taskId: string) => void;
}

export const RecurringTaskSeriesComponent: React.FC<RecurringTaskSeriesProps> = ({
  taskId,
  isRecurring,
  onTaskSelect
}) => {
  const [series, setSeries] = useState<RecurringTaskSeries[]>([]);
  const [loading, setLoading] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isRecurring && taskId) {
      fetchSeries();
    }
  }, [taskId, isRecurring]);

  const fetchSeries = async () => {
    setLoading(true);
    setError(null);
    try {
      const seriesData = await getRecurringTaskSeries(taskId);
      setSeries(seriesData);
    } catch (err) {
      console.error('Error fetching recurring task series:', err);
      setError('Failed to load task series');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-500"><CheckCircle2 className="h-3 w-3 mr-1" />Completed</Badge>;
      case 'in_progress':
        return <Badge variant="default" className="bg-blue-500"><Clock className="h-3 w-3 mr-1" />In Progress</Badge>;
      case 'open':
      case 'new':
        return <Badge variant="outline">Open</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch {
      return dateString;
    }
  };

  if (!isRecurring) {
    return null;
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <History className="h-4 w-4 animate-spin" />
            <span className="text-sm text-muted-foreground">Loading task series...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2 text-red-600">
            <History className="h-4 w-4" />
            <span className="text-sm">{error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (series.length === 0) {
    return null;
  }

  const completedTasks = series.filter(task => task.status === 'completed');
  const pendingTasks = series.filter(task => task.status !== 'completed');

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Repeat className="h-4 w-4 text-blue-500" />
            <CardTitle className="text-base">Recurring Task Series</CardTitle>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setExpanded(!expanded)}
            className="h-8 w-8 p-0"
          >
            {expanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </div>
        <CardDescription>
          {completedTasks.length} completed, {pendingTasks.length} pending
        </CardDescription>
      </CardHeader>

      {expanded && (
        <CardContent className="pt-0">
          <div className="space-y-4">
            {/* Summary Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-green-50 rounded-lg border border-green-200">
                <div className="text-2xl font-bold text-green-700">{completedTasks.length}</div>
                <div className="text-sm text-green-600">Completed</div>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="text-2xl font-bold text-blue-700">{pendingTasks.length}</div>
                <div className="text-sm text-blue-600">Pending</div>
              </div>
            </div>

            <Separator />

            {/* Task List */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-muted-foreground">Task History</h4>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {series.map((task, index) => (
                  <div
                    key={task.id}
                    className={`flex items-center justify-between p-3 rounded-lg border transition-colors ${
                      onTaskSelect ? 'cursor-pointer hover:bg-muted/50' : ''
                    } ${task.id === taskId ? 'bg-blue-50 border-blue-200' : 'bg-background'}`}
                    onClick={() => onTaskSelect?.(task.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-xs font-mono bg-muted px-2 py-1 rounded">
                          #{task.recurrenceCount}
                        </span>
                        {task.isOriginal && (
                          <Badge variant="outline" className="text-xs">Original</Badge>
                        )}
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          {getStatusBadge(task.status)}
                          {task.id === taskId && (
                            <Badge variant="secondary" className="text-xs">Current</Badge>
                          )}
                        </div>
                        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                          <Calendar className="h-3 w-3" />
                          <span>Due: {formatDate(task.dueDate)}</span>
                          {task.completedAt && (
                            <>
                              <span>•</span>
                              <span>Completed: {formatDate(task.completedAt)}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default RecurringTaskSeriesComponent;
