import React, { useState, useEffect } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar as CalendarIcon, ExternalLink, RefreshCw, Loader2 } from 'lucide-react';

import { MaintenanceTask, Provider, MaintenanceSeverity, MaintenanceStatus } from '@/components/maintenance/types';

interface MaintenanceTaskFormProps {
  title: string;
  setTitle: (title: string) => void;
  description: string;
  setDescription: (description: string) => void;
  selectedPropertyId: string;
  setSelectedPropertyId: (id: string) => void;
  severity: MaintenanceSeverity;
  setSeverity: (severity: MaintenanceSeverity) => void;
  status: MaintenanceStatus;
  setStatus: (status: MaintenanceStatus) => void;
  dueDate: string;
  setDueDate: (date: string) => void;
  selectedProviderId: string;
  setSelectedProviderId: (id: string) => void;
  assignedTo: string;
  setAssignedTo: (assignedTo: string) => void;
  properties: Array<{ id: string; name: string; address: string }>;
  providers: Provider[];
  editMode?: boolean;
  nextCheckInDate?: string | null;
  checkOutDate?: string | null;
  isOccupied?: boolean;
  isSyncingCalendar?: boolean;
  onSyncCalendar?: () => Promise<boolean>;
}

const MaintenanceTaskForm: React.FC<MaintenanceTaskFormProps> = ({
  title,
  setTitle,
  description,
  setDescription,
  selectedPropertyId,
  setSelectedPropertyId,
  severity,
  setSeverity,
  status,
  setStatus,
  dueDate,
  setDueDate,
  selectedProviderId,
  setSelectedProviderId,
  assignedTo,
  setAssignedTo,
  properties,
  providers,
  editMode = false,
  nextCheckInDate,
  checkOutDate,
  isOccupied,
  isSyncingCalendar,
  onSyncCalendar,
}) => {
  const [propertyAddress, setPropertyAddress] = useState<string>('');

  useEffect(() => {
    if (selectedPropertyId && selectedPropertyId !== 'none') {
      const property = properties.find(p => p.id === selectedPropertyId);
      if (property) {
        setPropertyAddress(property.address);
      } else {
        setPropertyAddress('');
      }
    } else {
      setPropertyAddress('');
    }
  }, [selectedPropertyId, properties]);

  const addToGoogleCalendar = () => {
    if (!title || !dueDate) {
      return;
    }

    const eventTitle = `Maintenance: ${title}`;
    const details = description ? `Description: ${description}\n` : '';
    const locationText = propertyAddress ? `Location: ${propertyAddress}\n` : '';
    const eventDetails = `${details}${locationText}Severity: ${severity}`;
    
    const formattedDate = dueDate.replace(/-/g, '');
    
    const googleCalendarUrl = `https://www.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(eventTitle)}&dates=${formattedDate}/${formattedDate}&details=${encodeURIComponent(eventDetails)}&location=${encodeURIComponent(propertyAddress || '')}&sf=true&output=xml`;
    
    window.open(googleCalendarUrl, '_blank');
  };

  console.log('MaintenanceTaskForm rendered with booking status:', { 
    isOccupied, 
    checkOutDate, 
    nextCheckInDate 
  });

  return (
    <div className="grid gap-4 py-4 px-2">
      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="title" className="text-right">Title *</Label>
        <Input 
          id="title" 
          placeholder="Enter task title" 
          className="col-span-3"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          required
        />
      </div>
      
      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="property" className="text-right">Property</Label>
        <Select 
          value={selectedPropertyId || 'none'} 
          onValueChange={setSelectedPropertyId}
        >
          <SelectTrigger className="col-span-3">
            <SelectValue placeholder="Select a property" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">No property</SelectItem>
            {properties.map((property) => (
              <SelectItem key={property.id} value={property.id}>
                {property.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="propertyAddress" className="text-right">Address</Label>
        <Input 
          id="propertyAddress" 
          value={propertyAddress} 
          className="col-span-3"
          readOnly
        />
      </div>

      <div className="grid grid-cols-4 items-start gap-4">
        <Label htmlFor="description" className="text-right pt-2">Description *</Label>
        <Textarea 
          id="description" 
          placeholder="Enter task description" 
          className="col-span-3"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          rows={4}
          required
        />
      </div>
      
      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="severity" className="text-right">Severity</Label>
        <Select value={severity} onValueChange={(value) => setSeverity(value as MaintenanceTask['severity'])}>
          <SelectTrigger className="col-span-3">
            <SelectValue placeholder="Select severity" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="low">Low</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="high">High</SelectItem>
            <SelectItem value="critical">Critical</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {editMode && (
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="status" className="text-right">Status</Label>
          <Select value={status} onValueChange={(value) => setStatus(value as MaintenanceTask['status'])}>
            <SelectTrigger className="col-span-3">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="new">New</SelectItem>
              <SelectItem value="assigned">Assigned</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}
      
      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="dueDate" className="text-right">Due Date</Label>
        <div className="col-span-3 flex gap-2">
          <Input 
            id="dueDate" 
            type="date" 
            className="flex-1"
            value={dueDate}
            onChange={(e) => setDueDate(e.target.value)}
          />
          {dueDate && (
            <Button 
              type="button" 
              variant="outline" 
              size="icon" 
              onClick={addToGoogleCalendar}
              title="Add to Google Calendar"
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
      
      {selectedPropertyId && selectedPropertyId !== 'none' && (
        <div className="grid grid-cols-4 items-start gap-4">
          <Label className="text-right pt-2">Property Status</Label>
          <div className="col-span-3 space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex-1">
                {isOccupied ? (
                  <div className="p-2 border border-red-300 bg-red-50 text-red-700 dark:border-red-800/30 dark:bg-red-950/20 dark:text-red-400 rounded flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    <span className="font-medium">Property is currently occupied</span>
                    {checkOutDate && (
                      <span className="ml-1">until {checkOutDate}</span>
                    )}
                  </div>
                ) : (
                  <div className="p-2 border border-green-300 bg-green-50 text-green-700 dark:border-green-800/30 dark:bg-green-950/20 dark:text-green-400 rounded flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    <span className="font-medium">Property is currently vacant</span>
                  </div>
                )}
              </div>
              
              {onSyncCalendar && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="ml-2 flex-shrink-0"
                  onClick={onSyncCalendar}
                  disabled={isSyncingCalendar}
                >
                  {isSyncingCalendar ? (
                    <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  ) : (
                    <RefreshCw className="h-4 w-4 mr-1" />
                  )}
                  Sync Calendar
                </Button>
              )}
            </div>
            
            {nextCheckInDate && (
              <div className="p-2 border border-blue-300 bg-blue-50 text-blue-700 dark:border-blue-800/30 dark:bg-blue-950/20 dark:text-blue-400 rounded flex items-center">
                <CalendarIcon className="h-4 w-4 mr-2" />
                <span>Next guest check-in: {nextCheckInDate}</span>
              </div>
            )}
            
            {/* Only show "No upcoming bookings" when there truly are none */}
            {!nextCheckInDate && (
              <div className="p-2 border border-green-300 bg-green-50 text-green-700 dark:border-green-800/30 dark:bg-green-950/20 dark:text-green-400 rounded flex items-center">
                <CalendarIcon className="h-4 w-4 mr-2" />
                <span>No upcoming bookings</span>
              </div>
            )}
          </div>
        </div>
      )}
      
      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="provider" className="text-right">Provider</Label>
        <Select 
          value={selectedProviderId || 'none'} 
          onValueChange={setSelectedProviderId}
        >
          <SelectTrigger className="col-span-3">
            <SelectValue placeholder="Select a provider" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">No provider</SelectItem>
            {providers.map((provider) => (
              <SelectItem key={provider.id} value={provider.id}>
                {provider.name} {provider.email ? `(${provider.email})` : ''}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="assignedTo" className="text-right">Assigned To</Label>
        <Input 
          id="assignedTo" 
          placeholder="Enter name or email of assignee" 
          className="col-span-3"
          value={assignedTo}
          onChange={(e) => setAssignedTo(e.target.value)}
        />
      </div>
    </div>
  );
};

export default MaintenanceTaskForm;
