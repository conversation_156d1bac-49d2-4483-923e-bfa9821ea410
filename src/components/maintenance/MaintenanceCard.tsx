
import React, { useState } from 'react';
import { MaintenanceTask, MaintenanceStatus, Provider } from './types';
import { Card, CardContent } from '@/components/ui/card';
import GlassCard from '@/components/ui/GlassCard';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MoreHorizontal, CheckCircle, XCircle, AlertTriangle, Clock, CircleSlash, ThumbsUp, ThumbsDown, User } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

interface MaintenanceCardProps {
  task: MaintenanceTask;
  providers?: Provider[];
  onClick?: () => void;
  onStatusChange?: (taskId: string, newStatus: MaintenanceStatus) => Promise<boolean>;
  onDeleteTask?: (taskId: string) => Promise<boolean>;
}

const MaintenanceCard: React.FC<MaintenanceCardProps> = ({
  task,
  providers = [],
  onClick,
  onStatusChange,
  onDeleteTask
}) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const { authState } = useAuth();

  const handleStatusChange = async (newStatus: MaintenanceStatus) => {
    if (!onStatusChange) {
      console.error('onStatusChange handler not provided');
      toast.error('Status change handler not available');
      return;
    }

    console.log(`[MaintenanceCard] Attempting to change status from ${task.status} to ${newStatus} for task ${task.id}`);
    setIsUpdating(true);
    try {
      const success = await onStatusChange(task.id, newStatus);
      if (success) {
        console.log(`[MaintenanceCard] Successfully updated task ${task.id} status to ${newStatus}`);
        toast.success(`Task marked as ${newStatus}`);
      } else {
        console.error(`[MaintenanceCard] Failed to update task ${task.id} status to ${newStatus}`);
        toast.error('Failed to update task status - operation returned false');
      }
    } catch (error) {
      console.error('[MaintenanceCard] Exception updating task status:', error);
      toast.error(`Failed to update task status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDelete = async () => {
    if (!onDeleteTask) {
      console.error('onDeleteTask handler not provided');
      toast.error('Delete handler not available');
      return;
    }

    if (window.confirm('Are you sure you want to delete this task?')) {
      console.log(`[MaintenanceCard] Attempting to delete task ${task.id}`);
      setIsUpdating(true);
      try {
        const success = await onDeleteTask(task.id);
        if (success) {
          console.log(`[MaintenanceCard] Successfully deleted task ${task.id}`);
          toast.success('Task deleted successfully');
        } else {
          console.error(`[MaintenanceCard] Failed to delete task ${task.id}`);
          toast.error('Failed to delete task - operation returned false');
        }
      } catch (error) {
        console.error('[MaintenanceCard] Exception deleting task:', error);
        toast.error(`Failed to delete task: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } finally {
        setIsUpdating(false);
      }
    }
  };

  const getSeverityIcon = () => {
    switch (task.severity) {
      case 'low':
        return <Clock className="h-5 w-5 text-green-500" />;
      case 'medium':
        return <AlertTriangle className="h-5 w-5 text-amber-500" />;
      case 'high':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      case 'critical':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadgeColor = () => {
    switch (task.status) {
      case 'new': return 'bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white';
      case 'assigned': return 'bg-amber-500 hover:bg-amber-600 dark:bg-amber-600 dark:hover:bg-amber-700 text-white';
      case 'in_progress': return 'bg-purple-500 hover:bg-purple-600 dark:bg-purple-600 dark:hover:bg-purple-700 text-white';
      case 'completed': return 'bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 text-white';
      case 'cancelled': return 'bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-700 text-white';
      case 'accepted': return 'bg-emerald-500 hover:bg-emerald-600 dark:bg-emerald-600 dark:hover:bg-emerald-700 text-white';
      case 'rejected': return 'bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 text-white';
      default: return 'bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-700 text-white';
    }
  };

  const getProviderName = () => {
    if (!task.providerId) return null;
    const provider = providers.find(p => p.id === task.providerId);
    return provider?.name || null;
  };

  return (
    <div
      onClick={onClick}
      className="group bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-3 hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-200 cursor-pointer"
    >
      <div className="flex items-center justify-between gap-3">
        {/* Left side - Icon and main content */}
        <div className="flex items-center gap-3 min-w-0 flex-1">
          {/* Severity icon */}
          <div className="flex-shrink-0">
            {getSeverityIcon()}
          </div>

          {/* Main content - Compact single row layout */}
          <div className="min-w-0 flex-1">
            {/* Single row with all information */}
            <div className="flex items-center gap-2 flex-wrap">
              {/* Title */}
              <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate text-sm flex-shrink-0">
                {task.title}
              </h3>

              {/* Status Badge */}
              <Badge className={`${getStatusBadgeColor()} text-xs px-2 py-0.5 flex-shrink-0`}>
                {task.status}
              </Badge>

              {/* Property */}
              {task.propertyName && (
                <span className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 px-1.5 py-0.5 rounded text-xs truncate max-w-20 sm:max-w-24 flex-shrink-0">
                  {task.propertyName}
                </span>
              )}

              {/* Provider */}
              {getProviderName() && (
                <span className="bg-blue-100 dark:bg-blue-700 text-blue-700 dark:text-blue-200 px-1.5 py-0.5 rounded text-xs truncate max-w-20 sm:max-w-24 flex-shrink-0 flex items-center gap-1">
                  <User className="h-3 w-3" />
                  <span className="hidden sm:inline">{getProviderName()}</span>
                  <span className="sm:hidden">{getProviderName()?.substring(0, 8)}</span>
                </span>
              )}

              {/* Due Date */}
              {task.dueDate && task.dueDate !== 'No due date' && (
                <span className="flex-shrink-0 text-xs text-gray-500 dark:text-gray-400">
                  <span className="hidden sm:inline">Due: </span>
                  {new Date(task.dueDate).toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: new Date(task.dueDate).getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
                  })}
                </span>
              )}

              {/* Recurring indicator */}
              {task.isRecurring && (
                <span className="flex-shrink-0 text-purple-600 dark:text-purple-400 text-xs">
                  🔄<span className="hidden sm:inline ml-0.5">Recurring</span>
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Right side - Actions */}
        <div className="flex items-center gap-1 flex-shrink-0">
          {/* Quick action buttons for mobile */}
          <div className="flex sm:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handleStatusChange('completed');
              }}
              disabled={isUpdating}
              className="h-8 w-8 p-0"
            >
              <CheckCircle className="h-4 w-4 text-green-500" />
            </Button>
          </div>

          {/* Dropdown menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
              <Button variant="ghost" size="sm" disabled={isUpdating} className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {/* Status options available to all users */}
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleStatusChange('completed'); }}>
                <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                Mark as Completed
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleStatusChange('in_progress'); }}>
                <Clock className="h-4 w-4 mr-2 text-purple-500" />
                Mark as In Progress
              </DropdownMenuItem>

              {/* Options only available to non-service providers */}
              {authState?.profile?.role !== 'service_provider' && (
                <>
                  <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleStatusChange('cancelled'); }}>
                    <CircleSlash className="h-4 w-4 mr-2 text-gray-500" />
                    Cancel Task
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete();
                    }}
                    className="text-red-500 focus:text-red-500"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
};

export default MaintenanceCard;
