import { useState, useEffect } from 'react';
import { usePermissionManagement } from '@/hooks/usePermissionManagement';
import { useTeamData } from './hooks/useTeamData';
import { PermissionType } from '@/types/auth';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, UserCheck, UserX, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface PermissionManagementProps {
  teamId: string;
}

const PermissionManagement: React.FC<PermissionManagementProps> = ({ teamId }) => {
  const { teamMembers, canInviteMembers, loading: teamDataLoading } = useTeamData(teamId);
  const { permissions, loading: permissionsLoading, fetchUserPermissions, addUserPermission, updateUserPermission, removeUserPermission } = usePermissionManagement();
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [selectedPermission, setSelectedPermission] = useState<PermissionType | ''>( '');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Clear state when teamId changes
  useEffect(() => {
    if (teamId) {
      // Clear selected user when team changes
      setSelectedUserId(null);
      setError(null);
    }
  }, [teamId]);

  // Team members are already loaded by useTeamData hook
  // No need to fetch them again here

  // Set initial selected user when teamMembers are loaded or change
  useEffect(() => {
    if (teamMembers.length > 0 && !selectedUserId) {
      setSelectedUserId(teamMembers[0].user_id);
    }
  }, [teamMembers, selectedUserId]);

  useEffect(() => {
    if (selectedUserId && teamId) {
      fetchUserPermissions(selectedUserId, teamId).catch(err => {
        console.error('[PermissionManagement] Error loading permissions:', err);
        setError('Failed to load user permissions');
      });
    }
  }, [selectedUserId, teamId, fetchUserPermissions]);

  const handleAddPermission = async () => {
    if (!selectedUserId || !selectedPermission || !teamId) {
      toast.error('Please select a user and permission');
      return;
    }

    setIsLoading(true);
    try {
      const success = await addUserPermission(selectedUserId, selectedPermission, teamId);
      if (success) {
        toast.success('Permission added successfully');
        setSelectedPermission('');
        await fetchUserPermissions(selectedUserId, teamId);
      } else {
        toast.error('Failed to add permission');
      }
    } catch (error) {
      console.error('Error adding permission:', error);
      toast.error('Failed to add permission');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemovePermission = async (permissionId: string) => {
    if (!selectedUserId || !teamId) return;

    setIsLoading(true);
    try {
      const success = await removeUserPermission(permissionId, selectedUserId, teamId);
      if (success) {
        toast.success('Permission removed successfully');
        await fetchUserPermissions(selectedUserId, teamId);
      } else {
        toast.error('Failed to remove permission');
      }
    } catch (error) {
      console.error('Error removing permission:', error);
      toast.error('Failed to remove permission');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTogglePermission = async (permissionId: string, enabled: boolean) => {
    if (!selectedUserId || !teamId) return;

    setIsLoading(true);
    try {
      const success = await updateUserPermission(permissionId, enabled, selectedUserId, teamId);
      if (success) {
        toast.success(`Permission ${enabled ? 'enabled' : 'disabled'} successfully`);
        await fetchUserPermissions(selectedUserId, teamId);
      } else {
        toast.error('Failed to update permission');
      }
    } catch (error) {
      console.error('Error updating permission:', error);
      toast.error('Failed to update permission');
    } finally {
      setIsLoading(false);
    }
  };

  if (!canInviteMembers) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          You don't have permission to manage team permissions.
        </AlertDescription>
      </Alert>
    );
  }

  const userPermissions = permissions.filter(p => p.user_id === selectedUserId && p.team_id === teamId);

  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Team Permissions</CardTitle>
          <CardDescription className="text-sm">
            Manage permissions for team members
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3 pt-0">
          <div className="space-y-2">
            <label className="text-sm font-medium">Select Team Member</label>
            <Select value={selectedUserId || ''} onValueChange={setSelectedUserId} disabled={teamDataLoading}>
              <SelectTrigger>
                <SelectValue placeholder={teamDataLoading ? "Loading team members..." : teamMembers.length === 0 ? "No team members found" : "Choose a team member"} />
              </SelectTrigger>
              <SelectContent>
                {teamMembers.map(member => (
                  <SelectItem key={member.user_id} value={member.user_id}>
                    {member.first_name} {member.last_name} ({member.email})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedUserId && (
            <div className="space-y-4">
              <div className="flex gap-2">
                <Select value={selectedPermission} onValueChange={(value) => setSelectedPermission(value as PermissionType | '')}>
                  <SelectTrigger className="flex-1">
                    <SelectValue placeholder="Select permission to add" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(PermissionType).map(permission => (
                      <SelectItem key={permission} value={permission}>
                        {permission.replace(/_/g, ' ').toLowerCase()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  onClick={handleAddPermission}
                  disabled={!selectedPermission || isLoading}
                >
                  {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Add'}
                </Button>
              </div>

              <div className="space-y-2">
                <h4 className="text-sm font-medium">Current Permissions</h4>
                {permissionsLoading ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm text-muted-foreground">Loading permissions...</span>
                  </div>
                ) : userPermissions.length > 0 ? (
                  <div className="space-y-2">
                    {userPermissions.map(permission => (
                      <div key={permission.id} className="flex items-center justify-between p-2 border rounded">
                        <div className="flex items-center gap-2">
                          <Badge variant={permission.enabled ? 'default' : 'secondary'}>
                            {permission.permission.replace(/_/g, ' ').toLowerCase()}
                          </Badge>
                          {permission.enabled ? (
                            <UserCheck className="h-4 w-4 text-green-500" />
                          ) : (
                            <UserX className="h-4 w-4 text-gray-400" />
                          )}
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleTogglePermission(permission.id, !permission.enabled)}
                            disabled={isLoading}
                          >
                            {permission.enabled ? 'Disable' : 'Enable'}
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleRemovePermission(permission.id)}
                            disabled={isLoading}
                          >
                            Remove
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">No permissions assigned</p>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default PermissionManagement;