
import { useState, useEffect } from 'react';
import { useTeamManagement } from '@/hooks/useTeamManagement';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/hooks/usePermissions';
import { PermissionType } from '@/types/auth';
import { toast } from 'sonner';

export const useTeamData = (teamId: string) => {
  const { authState } = useAuth();
  const {
    teamMembers,
    teams,
    loading,
    fetchTeamMembers,
    inviteUserToTeam,
    removeTeamMember,
    sentInvitations,
    loadingSentInvitations,
    fetchSentInvitations,
    deleteInvitation,
    resendInvitation,
    teamProperties,
    assignPropertyToTeam,
    unassignPropertyFromTeam,
    fetchTeamProperties,
  } = useTeamManagement();
  const { hasPermission } = usePermissions();

  const [isProcessing, setIsProcessing] = useState(false);

  const isTeamOwner = teams.find(t => t.id === teamId)?.owner_id === authState.user?.id;
  const isAdmin = authState?.profile?.role === 'admin' || authState?.profile?.is_super_admin;
  const isPropertyManager = authState?.profile?.role === 'property_manager';

  const canManageStaff = isTeamOwner || isAdmin || isPropertyManager || hasPermission(PermissionType.MANAGE_STAFF, teamId);
  const canManageProviders = isTeamOwner || isAdmin || isPropertyManager || hasPermission(PermissionType.MANAGE_SERVICE_PROVIDERS, teamId);
  const canInviteMembers = isTeamOwner || isAdmin || isPropertyManager || canManageStaff || canManageProviders;

  useEffect(() => {
    if (teamId) {
      // Add a small delay to prevent rapid successive calls
      const timeoutId = setTimeout(() => {
        fetchTeamMembers(teamId).catch(err => {
          console.error('Error loading team members:', err);
          toast.error('Failed to load team members');
        });
        fetchSentInvitations(teamId).catch(err => {
          console.error('Error loading sent invitations:', err);
          toast.error('Failed to load sent invitations');
        });
        fetchTeamProperties(teamId).catch(err => {
          console.error('Error loading team properties:', err);
          toast.error('Failed to load team properties');
        });
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [teamId]); // Removed function dependencies to prevent loops

  return {
    // Team Data
    teamMembers,
    teams,
    sentInvitations,
    teamProperties,

    // Loading States
    loading,
    loadingSentInvitations,
    isProcessing,

    // User Permissions
    isTeamOwner,
    isAdmin,
    isPropertyManager,
    canManageStaff,
    canManageProviders,
    canInviteMembers,

    // Functions
    fetchTeamMembers,
    inviteUserToTeam,
    removeTeamMember,
    fetchSentInvitations,
    deleteInvitation,
    resendInvitation,
    assignPropertyToTeam,
    unassignPropertyFromTeam,
    setIsProcessing,
    authState,
  };
};
