
import React from 'react';
import { DamageReport } from '@/types/damages';
import InvoiceDetailsDialog from '../InvoiceDetailsDialog';
import CreateInvoiceDialog from '../invoice/CreateInvoiceDialog';
import UploadInvoiceDialog from '../invoice/UploadInvoiceDialog';
import InvoiceHeader from './invoice/InvoiceHeader';
import InvoiceList from './invoice/InvoiceList';
import { useInvoiceManager } from './invoice/useInvoiceManager';

interface Property {
  id: string;
  name: string;
  address?: string;
  city?: string;
  state?: string;
}

interface Provider {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  specialty?: string;
}

interface InvoiceTabProps {
  damageReport: DamageReport;
  providers: Provider[];
}

const InvoiceTab: React.FC<InvoiceTabProps> = ({ damageReport, providers }) => {
  const {
    invoices,
    isLoading,
    isCreateDialogOpen,
    setIsCreateDialogOpen,
    isUploadDialogOpen,
    setIsUploadDialogOpen,
    isEditDialogOpen,
    setIsEditDialogOpen,
    selectedInvoiceId,
    setSelectedInvoiceId,
    fetchInvoices,
    handleCreateInvoice,
    handleUploadInvoice,
    handleViewInvoice,
    handleViewFullInvoice,
    handleDownloadInvoice,
    handleEditInvoice
  } = useInvoiceManager(damageReport.id);

  return (
    <div className="space-y-4">
      <InvoiceHeader 
        onCreateInvoice={handleCreateInvoice}
        onUploadInvoice={handleUploadInvoice}
      />

      <InvoiceList
        invoices={invoices}
        isLoading={isLoading}
        onViewInvoice={handleViewInvoice}
        onViewFullInvoice={handleViewFullInvoice}
        onDownloadInvoice={handleDownloadInvoice}
        onCreateInvoice={handleCreateInvoice}
        onUploadInvoice={handleUploadInvoice}
        onEditInvoice={handleEditInvoice}
      />

      <CreateInvoiceDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        reportId={damageReport.id}
        onCreated={fetchInvoices}
        providers={providers}
      />

      <UploadInvoiceDialog
        open={isUploadDialogOpen}
        onOpenChange={setIsUploadDialogOpen}
        damageReportId={damageReport.id}
        onUploaded={fetchInvoices}
        providers={providers}
      />

      <InvoiceDetailsDialog
        open={!!selectedInvoiceId}
        onOpenChange={() => setSelectedInvoiceId(null)}
        invoiceId={selectedInvoiceId || ''}
        damageReport={damageReport}
        onInvoiceUpdated={fetchInvoices}
      />

      <CreateInvoiceDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        reportId={damageReport.id}
        onCreated={fetchInvoices}
        providers={providers}
        existingInvoiceId={selectedInvoiceId || ''}
        isEditing={true}
      />
    </div>
  );
};

export default InvoiceTab;
