import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { formatDistanceToNow, format } from 'date-fns';
import { Separator } from '@/components/ui/separator';
import { DamageReport } from '@/types/damages';
import { Building, Calendar, Clock, User, Edit, Save, X } from 'lucide-react';

interface Property {
  id: string;
  name: string;
}

interface Provider {
  id: string;
  name: string;
  specialty?: string;
}

interface OverviewTabProps {
  damageReport: DamageReport;
  property: Property | null;
  properties: Property[];
  providers: Provider[];
  onUpdateReport: (updatedReport: Partial<DamageReport>) => Promise<void>;
}

const statusOptions = [
  { value: 'open', label: 'Open' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'resolved', label: 'Resolved' },
  { value: 'closed', label: 'Closed' }
];

const platformOptions = ["Airbnb", "VRBO", "Booking.com", "Expedia", "Other"];

const OverviewTab = ({ 
  damageReport, 
  property, 
  properties,
  providers,
  onUpdateReport 
}: OverviewTabProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [title, setTitle] = useState(damageReport.title);
  const [description, setDescription] = useState(damageReport.description);
  const [propertyId, setPropertyId] = useState(damageReport.property_id);
  const [providerId, setProviderId] = useState(damageReport.provider_id || 'none');
  const [status, setStatus] = useState(damageReport.status);
  const [platform, setPlatform] = useState(damageReport.platform || 'none');
  const [isSaving, setIsSaving] = useState(false);

  const reportedDate = new Date(damageReport.created_at);
  const lastUpdated = new Date(damageReport.updated_at);
  const timeAgo = formatDistanceToNow(lastUpdated, { addSuffix: true });
  
  const handleSave = async () => {
    setIsSaving(true);
    try {
      console.log("OverviewTab - Saving changes:", {
        title,
        description,
        property_id: propertyId,
        provider_id: providerId === 'none' ? undefined : providerId,
        status,
        platform: platform === 'none' ? undefined : platform
      });
      
      await onUpdateReport({
        title,
        description,
        property_id: propertyId,
        provider_id: providerId === 'none' ? undefined : providerId,
        status,
        platform: platform === 'none' ? undefined : platform
      });
      
      setIsEditing(false);
    } catch (error) {
      console.error("Error saving changes:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setTitle(damageReport.title);
    setDescription(damageReport.description);
    setPropertyId(damageReport.property_id);
    setProviderId(damageReport.provider_id || 'none');
    setStatus(damageReport.status);
    setPlatform(damageReport.platform || 'none');
    setIsEditing(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Damage Report Details</h3>
        {!isEditing ? (
          <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
            <Edit className="mr-2 h-4 w-4" /> Edit
          </Button>
        ) : (
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={handleCancel}>
              <X className="mr-2 h-4 w-4" /> Cancel
            </Button>
            <Button variant="default" size="sm" onClick={handleSave} disabled={isSaving}>
              <Save className="mr-2 h-4 w-4" /> {isSaving ? 'Saving...' : 'Save'}
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {isEditing ? (
              <>
                <div className="space-y-2">
                  <Label htmlFor="title">Title</Label>
                  <Input 
                    id="title" 
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea 
                    id="description" 
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={5}
                    className="w-full"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="property">Property</Label>
                  <Select value={propertyId} onValueChange={setPropertyId}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select property" />
                    </SelectTrigger>
                    <SelectContent>
                      {properties.map(prop => (
                        <SelectItem key={prop.id} value={prop.id}>
                          {prop.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </>
            ) : (
              <>
                <div>
                  <span className="text-muted-foreground text-sm">Title:</span>
                  <h4 className="text-base font-medium">{damageReport.title}</h4>
                </div>
                <div>
                  <span className="text-muted-foreground text-sm">Description:</span>
                  <p className="text-sm whitespace-pre-wrap">{damageReport.description}</p>
                </div>
                <div className="flex items-center">
                  <Building className="text-muted-foreground mr-2 h-4 w-4" />
                  <span>{damageReport.property_name}</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="text-muted-foreground mr-2 h-4 w-4" />
                  <span>Reported on {format(reportedDate, 'PPP')}</span>
                </div>
                <div className="flex items-center">
                  <Clock className="text-muted-foreground mr-2 h-4 w-4" />
                  <span>Last updated {timeAgo}</span>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Status & Assignment</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {isEditing ? (
              <>
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select value={status} onValueChange={setStatus}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      {statusOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="provider">Assigned Provider</Label>
                  <Select value={providerId} onValueChange={setProviderId}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Assign a provider (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No Provider</SelectItem>
                      {providers.map(provider => (
                        <SelectItem key={provider.id} value={provider.id}>
                          {provider.name}{provider.specialty ? ` (${provider.specialty})` : ''}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="platform">Platform</Label>
                  <Select value={platform} onValueChange={setPlatform}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select platform (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No Platform</SelectItem>
                      {platformOptions.map(plat => (
                        <SelectItem key={plat} value={plat}>{plat}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </>
            ) : (
              <>
                <div>
                  <span className="text-muted-foreground text-sm">Status:</span>
                  <div className="mt-1">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      status === 'open' ? 'bg-amber-100 text-amber-800' : 
                      status === 'in_progress' ? 'bg-blue-100 text-blue-800' : 
                      status === 'resolved' ? 'bg-green-100 text-green-800' : 
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {status === 'in_progress' ? 'In Progress' : 
                       status.charAt(0).toUpperCase() + status.slice(1)}
                    </span>
                  </div>
                </div>
                <div>
                  <span className="text-muted-foreground text-sm">Assigned Provider:</span>
                  <div className="flex items-center mt-1">
                    <User className="text-muted-foreground mr-2 h-4 w-4" />
                    <span>{damageReport.provider_name || 'Not assigned'}</span>
                  </div>
                </div>
                {damageReport.platform && (
                  <div>
                    <span className="text-muted-foreground text-sm">Platform:</span>
                    <p className="text-sm">{damageReport.platform}</p>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default OverviewTab;
