import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import OverviewTab from '@/components/damages/detail-tabs/OverviewTab';
import PhotosTab from '@/components/damages/detail-tabs/photos/PhotosTab';
import InvoiceTab from '@/components/damages/detail-tabs/InvoiceTab';
import NotesTab from '@/components/damages/detail-tabs/NotesTab';
import GenerateReportsTab from '@/components/damages/detail-tabs/GenerateReportsTab';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent,
  AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { Trash2, Loader2, AlertCircle } from 'lucide-react'; // Import Loader2, AlertCircle
import { generateDamageReportPdf } from './GenerateDamageReportPdf';
import { DamageReport, Property, Provider, DamagePhoto, DamageNote, Invoice } from '@/types/damages'; // Import types

interface DamageReportDetailDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  damageReportId: string | undefined;
}

const DamageReportDetailDialog: React.FC<DamageReportDetailDialogProps> = ({
  open,
  setOpen,
  damageReportId,
}) => {
  const navigate = useNavigate(); // Keep navigate for potential future use
  const [damageReport, setDamageReport] = useState<DamageReport | null>(null); // Use DamageReport type
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null); // Add error state
  const [properties, setProperties] = useState<Property[]>([]); // Use Property type
  const [providers, setProviders] = useState<Provider[]>([]); // Use Provider type
  const [property, setProperty] = useState<Property | null>(null); // Use Property type
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false); // Add deleting state
  const [photos, setPhotos] = useState<DamagePhoto[]>([]); // Use DamagePhoto type
  const [notes, setNotes] = useState<DamageNote[]>([]); // Use DamageNote type
  const [invoices, setInvoices] = useState<Invoice[]>([]); // Use Invoice type
  const [isExporting, setIsExporting] = useState(false);

  // Define fetch functions outside useEffect so they can be used elsewhere
  const fetchPhotos = async () => {
    try {
      console.log('Fetching photos for damage report:', damageReportId);

      // Try our RPC function first
      try {
        const { data: rpcData, error: rpcError } = await supabase.rpc(
          'get_damage_photos',
          { p_damage_report_id: damageReportId }
        );

        if (rpcError) {
          console.error('Error fetching photos with RPC:', rpcError);
          // Fall back to direct query
        } else if (rpcData && rpcData.length > 0) {
          console.log(`Found ${rpcData.length} photos with RPC`);

          // Format the photos with URLs
          const photosWithUrls = rpcData.map((photo: any) => ({
            ...photo,
            url: photo.public_url
          }));

          setPhotos(photosWithUrls);
          return;
        }
      } catch (rpcError) {
        console.error('Exception fetching photos with RPC:', rpcError);
      }

      // Fallback to direct query
      const { data, error } = await supabase
        .from('damage_photos')
        .select('*')
        .eq('damage_report_id', damageReportId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching photos:', error);
        return;
      }

      console.log(`Found ${data?.length || 0} photos`);

      if (data && data.length > 0) {
        // Create public URLs for all photos
        const photosWithUrls = data.map((photo: DamagePhoto) => {
          if (photo.file_path) {
            return {
              ...photo,
              url: `https://pwaeknalhosfwuxkpaet.supabase.co/storage/v1/object/public/damage-photos/${photo.file_path}`
            };
          }
          return photo;
        });
        setPhotos(photosWithUrls);
      } else {
        setPhotos([]);
      }
    } catch (error) {
      console.error('Error in fetchPhotos:', error);
      setPhotos([]);
    }
  };

  const fetchNotes = async () => {
    try {
      console.log('Fetching notes for damage report:', damageReportId);
      const { data, error } = await supabase
        .from('damage_notes')
        .select('*')
        .eq('damage_report_id', damageReportId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching notes:', error);
        return;
      }
      console.log(`Found ${data?.length || 0} notes`);
      setNotes(data || []);
    } catch (error) {
      console.error('Error in fetchNotes:', error);
      setNotes([]);
    }
  };

  useEffect(() => {
    if (!damageReportId) {
      setLoading(false);
      setError("No damage report ID provided.");
      return;
    }

    const fetchDamageReport = async () => {
      setLoading(true);
      setError(null); // Reset error on fetch
      try {
        const { data, error: fetchError } = await supabase
          .from('damage_reports')
          .select('*')
          .eq('id', damageReportId)
          .single();

        if (fetchError) {
          console.error('Error fetching damage report:', fetchError);
          setError(`Failed to load damage report: ${fetchError.message}`); // Set error state
          return;
        }

        setDamageReport(data);

        if (data.property_id) {
          const { data: propertyData, error: propertyError } = await supabase
            .from('properties')
            .select('*')
            .eq('id', data.property_id)
            .single();

          if (!propertyError && propertyData) {
            setProperty(propertyData);
          }
        }
      } catch (catchError: any) {
        console.error('Error fetching damage report:', catchError);
        setError(`An unexpected error occurred: ${catchError.message}`); // Set error state
      } finally {
        setLoading(false);
      }
    };

    const fetchProperties = async () => {
      try {
        const { data, error } = await supabase
          .from('properties')
          .select('*');

        if (!error && data) {
          setProperties(data);
        }
      } catch (error) {
        console.error('Error fetching properties:', error);
      }
    };

    const fetchProviders = async () => {
      try {
        const { data, error } = await supabase
          .from('maintenance_providers')
          .select('*');

        if (!error && data) {
          setProviders(data);
        }
      } catch (error) {
        console.error('Error fetching providers:', error);
      }
    };

    // fetchPhotos is now defined outside useEffect

    // fetchNotes is now defined outside useEffect

    const fetchInvoices = async () => {
      try {
        const { data, error } = await supabase
          .from('damage_invoices')
          .select('*')
          .eq('damage_report_id', damageReportId)
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching invoices:', error);
          return;
        }

        // If we have invoices, fetch their items
        if (data && data.length > 0) {
          const invoicesWithItems = await Promise.all(
            data.map(async (invoice: Invoice) => { // Use Invoice type
              const { data: itemsData, error: itemsError } = await supabase
                .from('invoice_items')
                .select('*')
                .eq('invoice_id', invoice.id)
                .order('created_at', { ascending: true });

              if (itemsError) {
                console.error(`Error fetching items for invoice ${invoice.id}:`, itemsError);
                return invoice;
              }

              return { ...invoice, items: itemsData || [] };
            })
          );
          setInvoices(invoicesWithItems);
        } else {
          setInvoices([]);
        }
      } catch (error) {
        console.error('Error in fetchInvoices:', error);
        setInvoices([]);
      }
    };

    // Execute all fetch operations
    fetchDamageReport();
    fetchProperties();
    fetchProviders();
    fetchPhotos();
    fetchNotes();
    fetchInvoices();
  }, [damageReportId]);

  const handleClose = () => {
    setOpen(false); // Just close the dialog
  };

  const handleUpdateReport = async (updatedData: Partial<DamageReport>) => { // Use Partial<DamageReport>
    if (!damageReportId) return;

    try {
      const { error } = await supabase
        .from('damage_reports')
        .update(updatedData)
        .eq('id', damageReportId);

      if (error) throw error;

      setDamageReport((prev) => (prev ? {...prev, ...updatedData} : null)); // Update state with type safety
      toast.success('Report updated successfully'); // Add success feedback

    } catch (error) {
      console.error('Error updating damage report:', error);
      toast.error('Failed to update report'); // Add error feedback
    }
  };

  const handleDelete = async () => {
    if (!damageReportId) return;

    setIsDeleting(true); // Set deleting state
    try {
      const { error } = await supabase
        .from('damage_reports')
        .delete()
        .eq('id', damageReportId);

      if (error) throw error;

      toast.success('Damage report deleted successfully');
      setOpen(false);
      navigate('/damages'); // Navigate after successful delete
    } catch (error) {
      console.error('Error deleting damage report:', error);
      toast.error('Failed to delete damage report');
    } finally {
      setIsDeleting(false); // Reset deleting state
      setIsDeleteDialogOpen(false); // Close confirmation dialog regardless of outcome
    }
  };

  const handleExportReport = async (options: {
    includePhotos: boolean;
    includeNotes: boolean;
    includeInvoices: boolean;
  }) => {
    if (!damageReport) return;

    setIsExporting(true);
    try {
      // Use provider from state if available, ensure it's undefined if not found or no ID
      const providerDetails = damageReport.provider_id
        ? providers.find(p => p.id === damageReport.provider_id) || undefined
        : undefined;

      await generateDamageReportPdf(
        damageReport,
        options.includePhotos ? photos : [],
        options.includeNotes ? notes : [],
        options.includeInvoices ? invoices : [],
        property || undefined, // Convert null to undefined
        providerDetails, // Now correctly typed as Provider | undefined
        options
      );

      toast.success('Report downloaded successfully');
    } catch (error) {
      console.error('Error exporting PDF:', error);
      toast.error('Failed to generate report');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-full w-full h-full overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Damage Report Details</DialogTitle>
            <DialogDescription>
              {loading ? 'Loading damage report...' : (error ? 'Error loading report' : 'View details and manage this damage report.')}
            </DialogDescription>
          </DialogHeader>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center h-64 text-destructive bg-destructive/10 p-4 rounded-md">
              <AlertCircle className="h-8 w-8 mb-2" />
              <p className="font-semibold">Error Loading Report</p>
              <p className="text-sm text-center">{error}</p>
              <Button variant="outline" size="sm" onClick={handleClose} className="mt-4">
                Close
              </Button>
            </div>
          ) : damageReport ? (
            <>
              <Tabs defaultValue="overview" className="space-y-4" onValueChange={(value) => {
                // Refresh data when switching to specific tabs
                if (value === 'photos') {
                  fetchPhotos();
                } else if (value === 'notes') {
                  fetchNotes();
                }
              }}>
                <TabsList>
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="photos">Photos</TabsTrigger>
                  <TabsTrigger value="notes">Notes</TabsTrigger>
                  <TabsTrigger value="invoice">Invoice</TabsTrigger>
                  <TabsTrigger value="generate-reports">Generate Reports</TabsTrigger>
                </TabsList>
                <TabsContent value="overview">
                  <OverviewTab
                    damageReport={damageReport}
                    property={property}
                    properties={properties}
                    providers={providers}
                    onUpdateReport={handleUpdateReport}
                  />
                </TabsContent>
                <TabsContent value="photos">
                  <PhotosTab
                    reportId={damageReportId || ''}
                  />
                </TabsContent>
                <TabsContent value="notes">
                  <NotesTab
                    reportId={damageReportId || ''}
                    initialNotes={notes}
                  />
                </TabsContent>
                <TabsContent value="invoice">
                  {/* Consider passing fetched invoices down */}
                  <InvoiceTab damageReport={damageReport} providers={providers} />
                </TabsContent>
                <TabsContent value="generate-reports">
                  <GenerateReportsTab
                    isExporting={isExporting}
                    onExport={handleExportReport}
                  />
                </TabsContent>
              </Tabs>
              <div className="flex justify-between mt-4">
                <div className="flex space-x-2">
                  <Button variant="secondary" onClick={handleClose}>
                    Close
                  </Button>
                </div>
                <Button
                  variant="outline"
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  onClick={() => setIsDeleteDialogOpen(true)}
                  disabled={isDeleting} // Disable if delete is in progress
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Report
                </Button>
              </div>
            </>
          ) : (
             <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
               <p>Damage report not found.</p>
                <Button variant="outline" size="sm" onClick={handleClose} className="mt-4">
                  Close
                </Button>
             </div>
          )}
        </DialogContent>
      </Dialog>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this report?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the damage report
              {damageReport?.title ? ` "${damageReport.title}"` : ''}.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting} // Disable while deleting
              className="bg-red-600 text-white hover:bg-red-700 flex items-center gap-2"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default DamageReportDetailDialog;
