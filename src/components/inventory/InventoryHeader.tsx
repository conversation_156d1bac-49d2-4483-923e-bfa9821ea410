
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Search,
  Plus,
  ShoppingCart,
  Upload,
  Filter,
  RefreshCw,
  Package,
  Trash2
} from 'lucide-react';
import { StandardPageHeader, PageHeaderButtons } from '@/components/ui/StandardizedUI';
import AdvancedSearch, { SearchCriteria } from './AdvancedSearch';
import ExportInventory from './ExportInventory';
import { Property, FormattedInventoryItem } from '@/types/inventory';

interface InventoryHeaderProps {
  onAddItem: () => void;
  onCreateOrder: () => void;
  onBulkImport: () => void;
  setSearchQuery: (query: string) => void;
  searchQuery: string;
  toggleFilters: () => void;
  onRefresh?: () => void;
  isLoading?: boolean;
  onAdvancedSearch?: (criteria: SearchCriteria) => void;
  properties?: Property[];
  collections?: string[];
  allItems?: FormattedInventoryItem[];
  filteredItems?: FormattedInventoryItem[];
}

const InventoryHeader: React.FC<InventoryHeaderProps> = ({
  onAddItem,
  onCreateOrder,
  onBulkImport,
  setSearchQuery,
  searchQuery,
  toggleFilters,
  onRefresh,
  isLoading = false,
  onAdvancedSearch,
  properties = [],
  collections = [],
  allItems = [],
  filteredItems = []
}) => {
  return (
    <StandardPageHeader
      title="Inventory"
      description="Manage your inventory items"
      searchQuery={searchQuery}
      onSearchChange={setSearchQuery}
      searchPlaceholder="Search inventory..."
      onRefresh={onRefresh}
      isLoading={isLoading}
      onToggleFilters={toggleFilters}
      secondaryActions={
        <>
          {/* Mobile-optimized secondary actions */}
          <Button
            variant="outline"
            size="sm"
            onClick={onBulkImport}
            className="flex-1 sm:flex-none min-w-0 flex items-center justify-center gap-2"
            disabled={isLoading}
          >
            <Upload className="h-4 w-4 flex-shrink-0" />
            <span className="truncate">Import</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onCreateOrder}
            className="flex-1 sm:flex-none min-w-0 flex items-center justify-center gap-2"
          >
            <ShoppingCart className="h-4 w-4 flex-shrink-0" />
            <span className="truncate">Order</span>
          </Button>

          {/* Advanced features - mobile dropdown or separate row */}
          <div className="hidden sm:flex gap-2">
            {onAdvancedSearch && (
              <AdvancedSearch
                onSearch={onAdvancedSearch}
                properties={properties}
                collections={collections}
              />
            )}
            <ExportInventory
              items={allItems}
              filteredItems={filteredItems}
            />
          </div>

          {/* Mobile: Show these in a separate compact row */}
          <div className="sm:hidden w-full flex gap-2 mt-2">
            {onAdvancedSearch && (
              <div className="flex-1">
                <AdvancedSearch
                  onSearch={onAdvancedSearch}
                  properties={properties}
                  collections={collections}
                />
              </div>
            )}
            <div className="flex-1">
              <ExportInventory
                items={allItems}
                filteredItems={filteredItems}
              />
            </div>
          </div>
        </>
      }
      primaryActionLabel="Add Item"
      onPrimaryAction={onAddItem}
      primaryActionIcon={<Plus size={16} />}
    />
  );
};

export default InventoryHeader;
