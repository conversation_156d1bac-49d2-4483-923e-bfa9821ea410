
import React, { useState, useEffect } from 'react';
import { FormattedInventoryItem } from '@/types/inventory';
import InventoryCard from './InventoryCard';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

interface InventoryGridProps {
  items: FormattedInventoryItem[];
  onItemClick: (item: FormattedInventoryItem) => void;
  onDeleteItems: (itemIds: string[]) => void;
}

const InventoryGrid: React.FC<InventoryGridProps> = ({
  items,
  onItemClick,
  onDeleteItems
}) => {
  const isMobile = useIsMobile();
  const [selectedItems, setSelectedItems] = useState<Record<string, boolean>>({});
  const [selectionMode, setSelectionMode] = useState(false);

  // Preload all images
  useEffect(() => {
    // Preload all item images in the background
    items.forEach(item => {
      if (item.imageUrl) {
        const img = new Image();
        img.src = item.imageUrl;

        // If the main image fails, try Amazon image
        img.onerror = () => {
          if (item.amazonUrl) {
            try {
              // Extract the ASIN from the Amazon URL
              const asinMatch = item.amazonUrl.match(/\/([A-Z0-9]{10})(\/|\?|$)/);
              if (asinMatch && asinMatch[1]) {
                const asin = asinMatch[1];
                const amazonImageUrl = `https://m.media-amazon.com/images/I/${asin}.jpg`;
                const amazonImg = new Image();
                amazonImg.src = amazonImageUrl;
              }
            } catch (error) {
              console.error('Error preloading Amazon image:', error);
            }
          }
        };
      }
    });
  }, [items]);

  // Count of selected items
  const selectedCount = Object.values(selectedItems).filter(Boolean).length;

  // Toggle selection mode
  const toggleSelectionMode = () => {
    setSelectionMode(!selectionMode);
    if (selectionMode) {
      // Clear selections when exiting selection mode
      setSelectedItems({});
    }
  };

  // Toggle selection of an item
  const toggleSelectItem = (e: React.MouseEvent, itemId: string) => {
    e.stopPropagation(); // Prevent card click
    setSelectedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };

  // Select all items
  const selectAll = () => {
    const newSelected: Record<string, boolean> = {};
    items.forEach(item => {
      // Ensure item.id exists before using
      if (item.id) {
        newSelected[item.id] = true;
      }
    });
    setSelectedItems(newSelected);
  };

  // Deselect all items
  const deselectAll = () => {
    setSelectedItems({});
  };

  // Delete selected items
  const handleDeleteSelected = () => {
    const itemIdsToDelete = Object.entries(selectedItems)
      .filter(([id, selected]) => selected)
      .map(([id]) => id);

    if (itemIdsToDelete.length > 0) {
      onDeleteItems(itemIdsToDelete);
      setSelectedItems({});
      setSelectionMode(false);
    }
  };

  // Handle individual card click
  const handleCardClick = (item: FormattedInventoryItem) => {
    if (selectionMode && item.id) {
      // In selection mode, clicking the card toggles selection
      setSelectedItems(prev => ({
        ...prev,
        [item.id!]: !prev[item.id!]
      }));
    } else {
      // Normal mode, open the item details
      onItemClick(item);
    }
  };

  return (
    <div className="space-y-4">
      {/* Selection mode controls - mobile optimized */}
      <div className={cn(
        "flex items-center mb-4",
        isMobile ? "flex-col gap-3" : "justify-between"
      )}>
        <Button
          variant="outline"
          onClick={toggleSelectionMode}
          className={cn(isMobile && "w-full")}
        >
          {selectionMode ? 'Exit Selection' : 'Select Items'}
        </Button>

        {selectionMode && (
          <div className={cn(
            "flex gap-2",
            isMobile ? "flex-col w-full" : "flex-wrap"
          )}>
            <div className={cn(
              "flex gap-2",
              isMobile && "w-full"
            )}>
              <Button
                variant="outline"
                size="sm"
                onClick={selectAll}
                disabled={selectedCount === items.length}
                className={cn(isMobile && "flex-1")}
              >
                Select All ({items.length})
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={deselectAll}
                disabled={selectedCount === 0}
                className={cn(isMobile && "flex-1")}
              >
                Clear Selection
              </Button>
            </div>
            
            {!isMobile && <div className="flex-1 min-w-0" />}
            
            <div className={cn(
              "flex items-center gap-2",
              isMobile && "w-full justify-between"
            )}>
              <span className="text-sm text-muted-foreground">
                {selectedCount} selected
              </span>
              <Button
                variant="destructive"
                size="sm"
                onClick={handleDeleteSelected}
                disabled={selectedCount === 0}
                className={cn(isMobile && "flex-1 ml-2")}
              >
                <Trash2 className="h-4 w-4 mr-1" />
                {isMobile ? `Delete (${selectedCount})` : `Delete (${selectedCount})`}
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Grid layout - mobile gets single column, desktop keeps responsive grid */}
      <div className={cn(
        "gap-4",
        isMobile 
          ? "flex flex-col space-y-4" // Mobile: single column with consistent spacing
          : "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" // Desktop: responsive grid
      )}>
        {items.map((item) => (
          <div
            key={item.id}
            className="relative"
          >
            {/* Mobile-optimized selection checkbox */}
            {selectionMode && item.id && (
              <Checkbox
                checked={!!selectedItems[item.id]}
                onCheckedChange={(checked) => {
                  setSelectedItems(prev => ({
                    ...prev,
                    [item.id!]: !!checked
                  }));
                }}
                onClick={(e) => item.id && toggleSelectItem(e, item.id)}
                className={cn(
                  "absolute z-10 bg-white rounded-md shadow-lg",
                  isMobile 
                    ? "top-3 left-3 h-5 w-5" // Mobile: larger touch target
                    : "top-2 left-2"
                )}
              />
            )}
            <InventoryCard
              item={item}
              onClick={() => handleCardClick(item)}
              selected={!!item.id && !!selectedItems[item.id]}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default InventoryGrid;
