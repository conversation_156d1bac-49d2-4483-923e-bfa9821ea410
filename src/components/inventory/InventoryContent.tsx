
import React, { useEffect, useState } from 'react';
import { FormattedInventoryItem } from '@/types/inventory';
import InventoryGrid from './InventoryGrid';
import InventoryTable from './InventoryTable';
import { Button } from '@/components/ui/button';
import { RefreshCw, Grid, List, Package, Search } from 'lucide-react';
import EmptyState from '../ui/EmptyState';
import LoadingState from '../ui/LoadingState';
import ErrorState from '../ui/ErrorState';
import { FilterOptions } from '@/hooks/useInventoryFilters';
import { StandardEmptyState, StandardLoadingState, StandardErrorState, StandardViewToggle } from '@/components/ui/StandardizedUI';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

interface InventoryContentProps {
  isLoading: boolean;
  filteredItems: FormattedInventoryItem[];
  onItemClick: (item: FormattedInventoryItem) => void;
  onDeleteItems: (itemIds: string[]) => void;
  searchQuery: string;
  filters: FilterOptions;
  retryFetch: () => void;
  error: unknown;
  viewMode: 'grid' | 'list';
  onViewModeChange: (mode: 'grid' | 'list') => void;
}

const InventoryContent: React.FC<InventoryContentProps> = ({
  isLoading,
  filteredItems,
  onItemClick,
  onDeleteItems,
  searchQuery,
  filters,
  retryFetch,
  error,
  viewMode,
  onViewModeChange
}) => {
  const isMobile = useIsMobile();
  // State to track if grid view has been loaded at least once
  const [gridViewLoaded, setGridViewLoaded] = useState(false);

  // When view mode changes to grid, mark it as loaded
  useEffect(() => {
    if (viewMode === 'grid') {
      setGridViewLoaded(true);
    }
  }, [viewMode]);

  // Handle retry button click
  const handleRetryClick = () => {
    retryFetch();
  };

  if (isLoading) {
    return <StandardLoadingState message="Loading inventory items..." />;
  }

  if (error) {
    return (
      <StandardErrorState
        message="We couldn't load your inventory items. Please try again."
        onRetry={handleRetryClick}
        retryLabel="Retry Loading"
      />
    );
  }

  if (filteredItems.length === 0) {
    // If there's a search or filter but no results
    if (searchQuery || (filters.property && filters.property !== 'all') ||
        (filters.collection && filters.collection !== 'all') ||
        filters.stockStatus !== 'all') {
      return (
        <StandardEmptyState
          title="No matching items found"
          description="Try adjusting your search or filters to see more results"
          icon={<Search className="h-12 w-12 text-muted-foreground" />}
        />
      );
    }

    // If there are no items at all
    return (
      <StandardEmptyState
        title="No inventory items yet"
        description="Add your first inventory item to get started"
        icon={<Package className="h-12 w-12 text-muted-foreground" />}
      />
    );
  }

  return (
    <div>
      {/* Mobile-optimized controls */}
      <div className={cn(
        "flex mb-4",
        isMobile ? "flex-col gap-3" : "justify-end space-x-2"
      )}>
        {/* On mobile, hide table view option since horizontal cards work better */}
        {!isMobile && (
          <StandardViewToggle
            viewType={viewMode}
            onToggleView={onViewModeChange}
          />
        )}
        <Button
          variant="outline"
          size="sm"
          onClick={handleRetryClick}
          className={cn(
            "flex items-center gap-1",
            isMobile && "w-full justify-center"
          )}
        >
          <RefreshCw className="h-4 w-4 mr-1" />
          Refresh
        </Button>
      </div>

      {/* Mobile: Force grid view with mobile-optimized cards */}
      {isMobile ? (
        <InventoryGrid
          items={filteredItems}
          onItemClick={onItemClick}
          onDeleteItems={onDeleteItems}
        />
      ) : (
        <>
          {/* Desktop: Support both views */}
          <div style={{ display: viewMode === 'list' ? 'block' : 'none' }}>
            <InventoryTable
              items={filteredItems}
              onItemClick={onItemClick}
              onDeleteItems={onDeleteItems}
            />
          </div>

          {(viewMode === 'grid' || gridViewLoaded) && (
            <div style={{ display: viewMode === 'grid' ? 'block' : 'none' }}>
              <InventoryGrid
                items={filteredItems}
                onItemClick={onItemClick}
                onDeleteItems={onDeleteItems}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default InventoryContent;
