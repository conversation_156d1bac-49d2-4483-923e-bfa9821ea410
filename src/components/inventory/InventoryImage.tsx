import React, { useState, useEffect } from 'react';
import { Package } from 'lucide-react';
import { cn } from '@/lib/utils';

interface InventoryImageProps {
  imageUrl?: string;
  amazonUrl?: string;
  itemName: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showPlaceholder?: boolean;
}

const InventoryImage: React.FC<InventoryImageProps> = ({
  imageUrl,
  amazonUrl,
  itemName,
  className,
  size = 'md',
  showPlaceholder = true
}) => {
  const [imageSrc, setImageSrc] = useState<string | undefined>(imageUrl);
  const [imageError, setImageError] = useState(false);

  const sizeClasses = {
    sm: 'h-12 w-12',
    md: 'h-24 w-24',
    lg: 'h-32 w-32'
  };

  useEffect(() => {
    setImageSrc(imageUrl);
    setImageError(false);
  }, [imageUrl]);

  const handleImageError = () => {
    console.log(`[InventoryImage] Primary image failed: ${imageUrl}`);
    if (amazonUrl) {
      const asinMatch = amazonUrl.match(/\/([A-Z0-9]{10})(\/|\?|$)/);
      if (asinMatch && asinMatch[1]) {
        const asin = asinMatch[1];
        const amazonFallbackUrl = `https://m.media-amazon.com/images/I/${asin}.jpg`;
        setImageSrc(amazonFallbackUrl);
        return;
      }
    }

    const asinMatch = itemName.match(/\b([A-Z0-9]{10})\b/);
    if (asinMatch && asinMatch[1]) {
      const asin = asinMatch[1];
      const amazonFallbackUrl = `https://m.media-amazon.com/images/I/${asin}.jpg`;
      setImageSrc(amazonFallbackUrl);
      return;
    }

    setImageError(true);
  };

  if (imageError || !imageSrc) {
    if (!showPlaceholder) return null;
    
    return (
      <div className={cn(
        "flex items-center justify-center bg-muted rounded",
        sizeClasses[size],
        className
      )}>
        <Package className="h-6 w-6 text-muted-foreground" />
      </div>
    );
  }

  return (
    <img
      src={imageSrc}
      alt={itemName}
      className={cn(
        "object-cover rounded",
        sizeClasses[size],
        className
      )}
      loading="lazy"
      onError={handleImageError}
    />
  );
};

export default InventoryImage;
