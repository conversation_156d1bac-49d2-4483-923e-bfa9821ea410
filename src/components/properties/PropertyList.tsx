import React, { useEffect } from 'react';
import { Property } from '@/hooks/useProperties';
import { mapPropertyToCardProperty } from '@/utils/propertyUtils';
import { Bed, Bath, MapPin, Calendar, ExternalLink, AlertTriangle, Wrench, Package } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { PropertyStatisticsMap } from '@/hooks/usePropertyStatistics';

interface PropertyListProps {
  properties: Property[];
  onPropertyClick: (property: Property) => void;
  statistics?: PropertyStatisticsMap;
}

const PropertyList: React.FC<PropertyListProps> = ({ properties, onPropertyClick, statistics }) => {
  // Debug logging for property data
  useEffect(() => {
    console.log('[PropertyList] Properties count:', properties.length);
    if (properties.length > 0) {
      console.log('[PropertyList] Sample property data:', {
        name: properties[0].name,
        bedrooms: properties[0].bedrooms,
        bathrooms: properties[0].bathrooms,
        image_url: properties[0].image_url
      });
    }
  }, [properties]);

  return (
    <div className="space-y-4">
      {properties.map((property) => {
        // Ensure numeric values for bedrooms and bathrooms
        const bedrooms = typeof property.bedrooms === 'number' ? property.bedrooms :
                        parseInt(String(property.bedrooms || '1')) || 1;

        const bathrooms = typeof property.bathrooms === 'number' ? property.bathrooms :
                         parseInt(String(property.bathrooms || '1')) || 1;

        const propertyStats = statistics?.[property.id];

        return (
          <div
            key={property.id}
            onClick={() => onPropertyClick(property)}
            className="border rounded-lg p-4 hover:bg-muted/5 transition-colors cursor-pointer"
          >
            <div className="flex flex-col sm:flex-row sm:items-start">
              <div className="flex-1">
                <h3 className="text-lg font-semibold">{property.name}</h3>

                <div className="flex items-center text-muted-foreground text-sm mt-1">
                  <MapPin className="h-3.5 w-3.5 mr-1 flex-shrink-0" />
                  <span>{property.city}, {property.state}</span>
                </div>

                <div className="grid grid-cols-2 gap-y-2 gap-x-6 mt-2">
                  <div className="flex items-center text-sm">
                    <Bed className="h-3.5 w-3.5 mr-1 flex-shrink-0" />
                    <span>{bedrooms} Beds</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Bath className="h-3.5 w-3.5 mr-1 flex-shrink-0" />
                    <span>{bathrooms} Baths</span>
                  </div>
                </div>

                {property.next_booking && (
                  <div className="flex items-center text-sm text-muted-foreground mt-2">
                    <Calendar className="h-3.5 w-3.5 mr-1 flex-shrink-0" />
                    <span>Next Booking: {property.next_booking}</span>
                  </div>
                )}

                <div className="mt-3 flex flex-wrap gap-2">
                {property.is_occupied && (
                  <Badge variant="destructive" className="text-xs">
                    Occupied
                    {property.current_checkout && ` Until ${property.current_checkout}`}
                  </Badge>
                )}

                {property.collections && property.collections.length > 0 &&
                  property.collections.slice(0, 2).map((collection: any, index: number) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {typeof collection === 'string' ? collection : collection.name}
                    </Badge>
                  ))
                }

                {/* Status indicators */}
                {propertyStats && (
                  <>
                    {propertyStats.maintenanceTasks.critical > 0 && (
                      <div className="flex items-center gap-1 px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs font-medium">
                        <AlertTriangle className="h-3 w-3" />
                        <span>{propertyStats.maintenanceTasks.critical} Critical</span>
                      </div>
                    )}
                    {propertyStats.maintenanceTasks.high > 0 && (
                      <div className="flex items-center gap-1 px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs font-medium">
                        <Wrench className="h-3 w-3" />
                        <span>{propertyStats.maintenanceTasks.high} High</span>
                      </div>
                    )}
                    {propertyStats.damageReports.open > 0 && (
                      <div className="flex items-center gap-1 px-2 py-1 bg-amber-100 text-amber-700 rounded-full text-xs font-medium">
                        <AlertTriangle className="h-3 w-3" />
                        <span>{propertyStats.damageReports.open} Damage{propertyStats.damageReports.open > 1 ? 's' : ''}</span>
                      </div>
                    )}
                    {propertyStats.inventoryItems.outOfStock > 0 && (
                      <div className="flex items-center gap-1 px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs font-medium">
                        <Package className="h-3 w-3" />
                        <span>{propertyStats.inventoryItems.outOfStock} Out</span>
                      </div>
                    )}
                    {propertyStats.inventoryItems.lowStock > 0 && propertyStats.inventoryItems.outOfStock === 0 && (
                      <div className="flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium">
                        <Package className="h-3 w-3" />
                        <span>{propertyStats.inventoryItems.lowStock} Low</span>
                      </div>
                    )}
                  </>
                )}
                </div>
              </div>

              <button
                className="text-primary hover:text-primary/80 font-medium text-sm mt-3 sm:mt-0 sm:ml-4 flex items-center"
                onClick={(e) => {
                  e.stopPropagation();
                  onPropertyClick(property);
                }}
              >
                View Details
                <ExternalLink className="ml-1 h-3.5 w-3.5" />
              </button>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default PropertyList;