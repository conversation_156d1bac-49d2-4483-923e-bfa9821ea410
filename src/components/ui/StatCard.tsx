import { forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';
import GlassCard from './GlassCard';

interface StatCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  value: string | number;
  icon: LucideIcon;
  colorScheme?: 'blue' | 'amber' | 'purple' | 'green';
  trend?: {
    value: number;
    isPositive: boolean;
  };
  alert?: boolean;
  loading?: boolean;
  subtitle?: string;
  className?: string;
}

const StatCard = forwardRef<HTMLDivElement, StatCardProps>(
  ({ 
    title,
    value,
    icon: Icon,
    colorScheme = 'blue',
    trend,
    alert = false,
    loading = false,
    subtitle,
    className,
    onClick,
    ...props 
  }, ref) => {
    
    const getIconColorClass = () => {
      switch (colorScheme) {
        case 'blue':
          return 'text-blue-600 dark:text-blue-400';
        case 'amber':
          return 'text-amber-600 dark:text-amber-400';
        case 'purple':
          return 'text-purple-600 dark:text-purple-400';
        case 'green':
          return 'text-green-600 dark:text-green-400';
        default:
          return 'text-blue-600 dark:text-blue-400';
      }
    };

    const getIconBgClass = () => {
      switch (colorScheme) {
        case 'blue':
          return 'bg-blue-100 dark:bg-blue-900/30';
        case 'amber':
          return 'bg-amber-100 dark:bg-amber-900/30';
        case 'purple':
          return 'bg-purple-100 dark:bg-purple-900/30';
        case 'green':
          return 'bg-green-100 dark:bg-green-900/30';
        default:
          return 'bg-blue-100 dark:bg-blue-900/30';
      }
    };

    return (
      <GlassCard
        ref={ref}
        variant="stat"
        colorScheme={colorScheme}
        hoverEffect={!!onClick}
        glowEffect={alert}
        noPadding
        className={cn(
          "overflow-hidden group h-12 w-full min-w-0", // Fixed height for ultra-compact design with mobile optimization
          alert && "ring-1 ring-red-500/50",
          onClick && "cursor-pointer",
          className
        )}
        onClick={onClick}
        {...props}
      >
        {/* Horizontal Layout - Icon + Content + Trend */}
        <div className="flex items-center h-full px-2 gap-2">
          {/* Icon Section */}
          <div className={cn(
            "p-1 rounded transition-all duration-300 group-hover:scale-110 flex-shrink-0",
            getIconBgClass()
          )}>
            <Icon className={cn("h-4 w-4", getIconColorClass())} />
            {alert && (
              <div className="absolute -top-0.5 -right-0.5 w-2 h-2 bg-red-500 rounded-full animate-pulse" />
            )}
          </div>

          {/* Content Section - Mobile Optimized */}
          <div className="flex-1 min-w-0 overflow-hidden">
            <div className="flex items-baseline gap-2 min-w-0">
              <p className="text-lg font-bold text-foreground transition-all duration-300 group-hover:scale-105 flex-shrink-0">
                {loading ? (
                  <span className="inline-block w-8 h-4 bg-muted animate-pulse rounded" />
                ) : (
                  value
                )}
              </p>
              <p className="text-xs font-medium text-muted-foreground truncate min-w-0 flex-1">
                {title}
              </p>
            </div>
            {subtitle && (
              <p className="text-xs text-muted-foreground/70 leading-tight truncate">
                {subtitle}
              </p>
            )}
          </div>

          {/* Trend Section */}
          {trend && (
            <div className={cn(
              "flex items-center gap-1 px-1.5 py-0.5 rounded text-xs font-medium flex-shrink-0",
              trend.isPositive
                ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                : "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"
            )}>
              <span className="text-xs">
                {trend.isPositive ? "↗" : "↘"}
              </span>
              <span>{Math.abs(trend.value)}%</span>
            </div>
          )}
        </div>

        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-white/5 to-transparent dark:from-white/2 pointer-events-none" />
      </GlassCard>
    );
  }
);

StatCard.displayName = 'StatCard';

export default StatCard;
