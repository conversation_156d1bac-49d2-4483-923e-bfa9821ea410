import { describe, it, expect, beforeEach } from 'vitest';
import { 
  calculateNextDueDate, 
  shouldCreateNextRecurrence, 
  validateRecurringConfig,
  formatRecurrenceDescription 
} from '@/utils/recurringTasks';
import { MaintenanceTask, RecurringTaskConfig } from '@/components/maintenance/types';

describe('Recurring Tasks Utility Functions', () => {
  describe('calculateNextDueDate', () => {
    it('should calculate next due date correctly', () => {
      const currentDate = new Date('2024-01-01');
      const nextDate = calculateNextDueDate(currentDate, 30);
      
      expect(nextDate.getDate()).toBe(31);
      expect(nextDate.getMonth()).toBe(0); // January
      expect(nextDate.getFullYear()).toBe(2024);
    });

    it('should handle string dates', () => {
      const nextDate = calculateNextDueDate('2024-01-01', 7);
      
      expect(nextDate.getDate()).toBe(8);
      expect(nextDate.getMonth()).toBe(0); // January
    });

    it('should handle month boundaries', () => {
      const nextDate = calculateNextDueDate('2024-01-31', 1);
      
      expect(nextDate.getDate()).toBe(1);
      expect(nextDate.getMonth()).toBe(1); // February
    });
  });

  describe('shouldCreateNextRecurrence', () => {
    it('should return false for non-recurring tasks', () => {
      const task: MaintenanceTask = {
        id: '1',
        title: 'Test Task',
        description: 'Test',
        propertyName: 'Test Property',
        status: 'completed',
        severity: 'medium',
        dueDate: '2024-01-01',
        createdAt: '2024-01-01',
        isRecurring: false
      };

      expect(shouldCreateNextRecurrence(task)).toBe(false);
    });

    it('should return true for recurring tasks without max limit', () => {
      const task: MaintenanceTask = {
        id: '1',
        title: 'Test Task',
        description: 'Test',
        propertyName: 'Test Property',
        status: 'completed',
        severity: 'medium',
        dueDate: '2024-01-01',
        createdAt: '2024-01-01',
        isRecurring: true,
        recurrenceIntervalDays: 30,
        recurrenceCount: 5
      };

      expect(shouldCreateNextRecurrence(task)).toBe(true);
    });

    it('should return false when max recurrences reached', () => {
      const task: MaintenanceTask = {
        id: '1',
        title: 'Test Task',
        description: 'Test',
        propertyName: 'Test Property',
        status: 'completed',
        severity: 'medium',
        dueDate: '2024-01-01',
        createdAt: '2024-01-01',
        isRecurring: true,
        recurrenceIntervalDays: 30,
        recurrenceCount: 10,
        maxRecurrences: 10
      };

      expect(shouldCreateNextRecurrence(task)).toBe(false);
    });

    it('should return true when under max recurrences', () => {
      const task: MaintenanceTask = {
        id: '1',
        title: 'Test Task',
        description: 'Test',
        propertyName: 'Test Property',
        status: 'completed',
        severity: 'medium',
        dueDate: '2024-01-01',
        createdAt: '2024-01-01',
        isRecurring: true,
        recurrenceIntervalDays: 30,
        recurrenceCount: 5,
        maxRecurrences: 10
      };

      expect(shouldCreateNextRecurrence(task)).toBe(true);
    });
  });

  describe('validateRecurringConfig', () => {
    it('should pass validation for valid config', () => {
      const config: RecurringTaskConfig = {
        isRecurring: true,
        recurrenceIntervalDays: 30,
        maxRecurrences: 12
      };

      const errors = validateRecurringConfig(config);
      expect(errors).toHaveLength(0);
    });

    it('should fail validation for invalid interval', () => {
      const config: RecurringTaskConfig = {
        isRecurring: true,
        recurrenceIntervalDays: 0
      };

      const errors = validateRecurringConfig(config);
      expect(errors).toContain('Recurrence interval must be at least 1 day');
    });

    it('should fail validation for too large interval', () => {
      const config: RecurringTaskConfig = {
        isRecurring: true,
        recurrenceIntervalDays: 400
      };

      const errors = validateRecurringConfig(config);
      expect(errors).toContain('Recurrence interval cannot exceed 365 days');
    });

    it('should fail validation for invalid max recurrences', () => {
      const config: RecurringTaskConfig = {
        isRecurring: true,
        recurrenceIntervalDays: 30,
        maxRecurrences: 0
      };

      const errors = validateRecurringConfig(config);
      expect(errors).toContain('Maximum recurrences must be at least 1');
    });

    it('should pass validation for non-recurring tasks', () => {
      const config: RecurringTaskConfig = {
        isRecurring: false,
        recurrenceIntervalDays: 0 // This should be ignored
      };

      const errors = validateRecurringConfig(config);
      expect(errors).toHaveLength(0);
    });
  });

  describe('formatRecurrenceDescription', () => {
    it('should format daily recurrence', () => {
      const description = formatRecurrenceDescription(1);
      expect(description).toBe('Daily (indefinitely)');
    });

    it('should format weekly recurrence', () => {
      const description = formatRecurrenceDescription(7);
      expect(description).toBe('Weekly (indefinitely)');
    });

    it('should format monthly recurrence', () => {
      const description = formatRecurrenceDescription(30);
      expect(description).toBe('Monthly (indefinitely)');
    });

    it('should format custom interval', () => {
      const description = formatRecurrenceDescription(45);
      expect(description).toBe('Every 45 days (indefinitely)');
    });

    it('should include max recurrences', () => {
      const description = formatRecurrenceDescription(30, 12);
      expect(description).toBe('Monthly (12 times)');
    });

    it('should format biweekly recurrence', () => {
      const description = formatRecurrenceDescription(14);
      expect(description).toBe('Every 2 weeks (indefinitely)');
    });

    it('should format quarterly recurrence', () => {
      const description = formatRecurrenceDescription(90);
      expect(description).toBe('Quarterly (indefinitely)');
    });

    it('should format semiannual recurrence', () => {
      const description = formatRecurrenceDescription(180);
      expect(description).toBe('Every 6 months (indefinitely)');
    });

    it('should format annual recurrence', () => {
      const description = formatRecurrenceDescription(365);
      expect(description).toBe('Annually (indefinitely)');
    });
  });
});
