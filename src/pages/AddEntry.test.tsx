import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import AddEntry from './AddEntry';

describe('AddEntry', () => {
  test('renders the main heading', () => {
    render(<AddEntry />);
    const headingElement = screen.getByText(/Add New Entry/i);
    expect(headingElement).toBeInTheDocument();
  });

  test('renders all entry categories', () => {
    render(<AddEntry />);
    const inventoryCategory = screen.getByText(/Inventory Item/i);
    const maintenanceCategory = screen.getByText(/Maintenance Task/i);
    const damageCategory = screen.getByText(/Damage Report/i);
    const propertyCategory = screen.getByText(/Property/i);

    expect(inventoryCategory).toBeInTheDocument();
    expect(maintenanceCategory).toBeInTheDocument();
    expect(damageCategory).toBeInTheDocument();
    expect(propertyCategory).toBeInTheDocument();
  });

  test('shows the form when a category is selected', () => {
    render(<AddEntry />);
    const inventoryCategory = screen.getByText(/Inventory Item/i);
    fireEvent.click(inventoryCategory);

    const formHeading = screen.getByText(/Inventory Item/i);
    expect(formHeading).toBeInTheDocument();

    const titleInput = screen.getByLabelText(/Item Name/i);
    expect(titleInput).toBeInTheDocument();
  });
});
