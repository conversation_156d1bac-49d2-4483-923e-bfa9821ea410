import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter } from 'react-router-dom';
import { AuthContext } from '@/contexts/AuthContext';
import Damages from './Damages';
import { Toaster } from '@/components/ui/toaster';

// Mock the useDamageReportsQueryV2 hook
jest.mock('@/hooks/useDamageReportsQueryV2', () => ({
  useDamageReportsQueryV2: () => ({
    damageReports: [],
    loading: true,
    error: null,
    fetchDamageReports: jest.fn(),
    addDamageReport: jest.fn(),
    updateDamageReport: jest.fn(),
    deleteDamageReport: jest.fn(),
  }),
}));

// Mock the usePermissions hook
jest.mock('@/hooks/usePermissionsFixed', () => ({
  usePermissions: () => ({
    isAdmin: () => false,
    isOwner: () => false,
    userTeams: [],
    getAccessiblePropertyIds: jest.fn().mockResolvedValue([]),
  }),
}));

describe('Damages', () => {
  test('renders the damages page with loading state', () => {
    render(
      <MemoryRouter>
        <AuthContext.Provider value={{ authState: { user: { id: '123' }, isAuthenticated: true } }}>
          <Damages />
          <Toaster />
        </AuthContext.Provider>
      </MemoryRouter>
    );

    expect(screen.getByText(/Damage Reports/i)).toBeInTheDocument();
    expect(screen.getAllByRole('status')).toHaveLength(6); // Skeletons
  });
});
