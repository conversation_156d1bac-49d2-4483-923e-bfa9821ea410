import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import DamageDetail from './DamageDetail';

// Mock the useDamageDetail hook
jest.mock('@/hooks/useDamageDetail', () => ({
  useDamageDetail: () => ({
    report: null,
    properties: [],
    providers: [],
    isLoading: true,
    property: null,
    photos: [],
    notes: [],
    invoices: [],
    handleUpdateReport: jest.fn(),
    refreshPhotos: jest.fn(),
  }),
}));

describe('DamageDetail', () => {
  test('renders the loading skeleton when loading', () => {
    render(
      <MemoryRouter initialEntries={['/damages/123']}>
        <Routes>
          <Route path="/damages/:id" element={<DamageDetail />} />
        </Routes>
      </MemoryRouter>
    );

    expect(screen.getByRole('status')).toBeInTheDocument();
  });
});
