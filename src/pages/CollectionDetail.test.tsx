import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import CollectionDetail from './CollectionDetail';

describe('CollectionDetail', () => {
  test('renders the collection ID from the URL', () => {
    render(
      <MemoryRouter initialEntries={['/collection/123']}>
        <Routes>
          <Route path="/collection/:id" element={<CollectionDetail />} />
        </Routes>
      </MemoryRouter>
    );

    expect(screen.getByText(/Collection ID: 123/i)).toBeInTheDocument();
  });
});
