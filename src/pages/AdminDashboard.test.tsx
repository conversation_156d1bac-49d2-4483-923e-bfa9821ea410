import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { AuthContext } from '@/contexts/AuthContext';
import AdminDashboard from './AdminDashboard';
import { Toaster } from '@/components/ui/toaster';

// Mock the useAdmin hook
jest.mock('@/hooks/useAdmin', () => ({
  __esModule: true,
  default: () => ({
    fetchUsers: jest.fn().mockResolvedValue([]),
    impersonateUser: jest.fn().mockResolvedValue(true),
    isLoading: false,
    error: null,
    isSuperAdmin: true, // Mock that the user is a super admin
  }),
}));

describe('AdminDashboard', () => {
  const mockUser = {
    id: '1',
    email: '<EMAIL>',
    is_super_admin: true,
  };

  const authProviderValue = {
    authState: {
      isLoading: false,
      profile: mockUser,
      isImpersonating: false,
      user: { id: '1' },
    },
    startImpersonation: jest.fn(),
    logout: jest.fn(),
  };

  test('renders the admin dashboard for super admins', () => {
    render(
      <MemoryRouter initialEntries={['/admin']}>
        <AuthContext.Provider value={authProviderValue}>
          <Routes>
            <Route path="/admin" element={<AdminDashboard />} />
          </Routes>
          <Toaster />
        </AuthContext.Provider>
      </MemoryRouter>
    );

    expect(screen.getByText(/Admin Dashboard/i)).toBeInTheDocument();
    expect(screen.getByText(/Users/<USER>
    expect(screen.getByText(/Database/i)).toBeInTheDocument();
    expect(screen.getByText(/Security/i)).toBeInTheDocument();
    expect(screen.getByText(/Settings/i)).toBeInTheDocument();
  });
});
