import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import ContactDetail from './ContactDetail';

describe('ContactDetail', () => {
  test('renders the contact ID from the URL', () => {
    render(
      <MemoryRouter initialEntries={['/contacts/123']}>
        <Routes>
          <Route path="/contacts/:id" element={<ContactDetail />} />
        </Routes>
      </MemoryRouter>
    );

    expect(screen.getByText(/Contact ID: 123/i)).toBeInTheDocument();
  });
});
