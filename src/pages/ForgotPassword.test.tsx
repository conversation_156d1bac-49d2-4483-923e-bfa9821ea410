import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter } from 'react-router-dom';
import ForgotPassword from './ForgotPassword';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// Mock Supabase functions
jest.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      resetPasswordForEmail: jest.fn(),
      getSession: jest.fn(),
      signOut: jest.fn(),
    },
  },
}));

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

describe('ForgotPassword', () => {
  beforeEach(() => {
    // Reset mocks before each test
    supabase.auth.resetPasswordForEmail.mockClear();
    supabase.auth.getSession.mockClear();
    supabase.auth.signOut.mockClear();
    toast.success.mockClear();
    toast.error.mockClear();
  });

  test('renders the forgot password form', () => {
    render(
      <MemoryRouter>
        <ForgotPassword />
      </MemoryRouter>
    );

    expect(screen.getByText(/Reset Password/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Email/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Send reset link/i })).toBeInTheDocument();
  });

  test('sends reset email on form submission and shows success message', async () => {
    supabase.auth.resetPasswordForEmail.mockResolvedValueOnce({ error: null });
    supabase.auth.getSession.mockResolvedValueOnce({ data: { session: null }, error: null });

    render(
      <MemoryRouter>
        <ForgotPassword />
      </MemoryRouter>
    );

    fireEvent.change(screen.getByLabelText(/Email/i), { target: { value: '<EMAIL>' } });
    fireEvent.click(screen.getByRole('button', { name: /Send reset link/i }));

    await waitFor(() => {
      expect(supabase.auth.resetPasswordForEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        expect.any(Object)
      );
    });

    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith('Password reset email sent');
    });

    expect(screen.getByText(/We've sent a password reset email to/i)).toBeInTheDocument();
    expect(screen.getByText(/<EMAIL>/i)).toBeInTheDocument();
  });

  test('shows error message if sending reset email fails', async () => {
    const errorMessage = 'User not found';
    supabase.auth.resetPasswordForEmail.mockResolvedValueOnce({ error: new Error(errorMessage) });
    supabase.auth.getSession.mockResolvedValueOnce({ data: { session: null }, error: null });

    render(
      <MemoryRouter>
        <ForgotPassword />
      </MemoryRouter>
    );

    fireEvent.change(screen.getByLabelText(/Email/i), { target: { value: '<EMAIL>' } });
    fireEvent.click(screen.getByRole('button', { name: /Send reset link/i }));

    await waitFor(() => {
      expect(supabase.auth.resetPasswordForEmail).toHaveBeenCalled();
    });

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to send reset email');
    });

    expect(screen.queryByText(/We've sent a password reset email to/i)).not.toBeInTheDocument();
  });
});
