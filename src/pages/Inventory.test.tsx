import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter } from 'react-router-dom';
import Inventory from './Inventory';

// Mock all necessary hooks and external dependencies
jest.mock('@/hooks/useInventoryQueryV2', () => ({
  useInventoryQueryV2: () => ({
    inventoryItems: [],
    loading: true,
    error: null,
    fetchInventory: jest.fn(),
  }),
}));

jest.mock('@/hooks/useInventoryFilters', () => ({
  useInventoryFilters: () => ({
    filters: {},
    setFilters: jest.fn(),
    searchQuery: '',
    setSearchQuery: jest.fn(),
    filteredItems: [],
    collections: [],
  }),
}));

jest.mock('@/hooks/useInventoryOperations', () => ({
  useInventoryOperations: () => ({
    selectedItem: null,
    itemDialog: { isOpen: false, onOpen: jest.fn(), onClose: jest.fn() },
    purchaseOrderDialog: { isOpen: false, onOpen: jest.fn(), onClose: jest.fn() },
    bulkImportDialog: { isOpen: false, onOpen: jest.fn(), onClose: jest.fn() },
    handleItemClick: jest.fn(),
    handleAddItem: jest.fn(),
    handleCreatePurchaseOrder: jest.fn(),
    handleBulkImport: jest.fn(),
    handleSaveItem: jest.fn(),
    handleDeleteItems: jest.fn(),
    handleBulkSaveItems: jest.fn(),
  }),
}));

jest.mock('@/hooks/usePurchaseOrders', () => ({
  usePurchaseOrders: () => ({
    createPurchaseOrder: { mutate: jest.fn() },
  }),
}));

jest.mock('@tanstack/react-query', () => ({
  useQuery: () => ({
    data: [],
    refetch: jest.fn(),
    isLoading: true,
  }),
}));

jest.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        order: jest.fn().mockResolvedValue({ data: [], error: null }),
      })),
    })),
  },
}));

jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warning: jest.fn(),
  },
}));

jest.mock('@/utils/setupStorage', () => ({
  ensureStorageBuckets: jest.fn().mockResolvedValue(true),
  createStorageBuckets: jest.fn().mockResolvedValue(true),
}));

jest.mock('@/utils/forceCloseDialog', () => ({
  forceCloseDialog: jest.fn(),
}));

// Mock chrome API for extension messaging
Object.defineProperty(global, 'chrome', {
  value: {
    runtime: {
      sendMessage: jest.fn(),
      lastError: undefined,
    },
  },
  writable: true,
});

describe('Inventory', () => {
  test('renders the inventory page with loading state and tabs', () => {
    render(
      <MemoryRouter>
        <Inventory />
      </MemoryRouter>
    );

    expect(screen.getByText(/Inventory Management/i)).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: /Inventory/i })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: /Analytics/i })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: /Amazon Search/i })).toBeInTheDocument();

    // Check for loading indicators if present in sub-components
    // This might need to be more specific depending on how loading is rendered
    // For now, we'll just check for the main title and tabs
  });
});
