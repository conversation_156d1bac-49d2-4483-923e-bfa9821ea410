import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter } from 'react-router-dom';
import Contacts from './Contacts';

describe('Contacts', () => {
  test('renders the no contacts found message', () => {
    render(
      <MemoryRouter>
        <Contacts />
      </MemoryRouter>
    );

    expect(screen.getByText(/No contacts found/i)).toBeInTheDocument();
  });
});