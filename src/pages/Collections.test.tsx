import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter } from 'react-router-dom';
import { AuthContext } from '@/contexts/AuthContext';
import Collections from './Collections';
import { Toaster } from '@/components/ui/toaster';

// Mock the useAuth hook
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    authState: { user: { id: '123' } },
  }),
}));

describe('Collections', () => {
  test('renders the collections page with loading state', () => {
    render(
      <MemoryRouter>
        <AuthContext.Provider value={{ authState: { user: { id: '123' } } }}>
          <Collections />
          <Toaster />
        </AuthContext.Provider>
      </MemoryRouter>
    );

    expect(screen.getByText(/Property Collections/i)).toBeInTheDocument();
    expect(screen.getAllByRole('status')).toHaveLength(3); // Skeletons
  });
});
