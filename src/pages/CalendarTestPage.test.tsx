import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import CalendarTestPage from './CalendarTestPage';

describe('CalendarTestPage', () => {
  test('renders the calendar test page', () => {
    render(<CalendarTestPage />);
    expect(screen.getByText(/Calendar Test Page/i)).toBeInTheDocument();
  });

  test('renders the basic calendar', () => {
    render(<CalendarTestPage />);
    expect(screen.getByText(/Basic Calendar/i)).toBeInTheDocument();
  });

  test('selects a date when a day is clicked', () => {
    render(<CalendarTestPage />);
    const today = new Date();
    const day = today.getDate().toString();
    fireEvent.click(screen.getByText(day, { selector: 'button' }));
    expect(screen.getByText(today.toLocaleDateString())).toBeInTheDocument();
  });
});
