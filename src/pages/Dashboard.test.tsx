import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter } from 'react-router-dom';
import { AuthContext } from '@/contexts/AuthContext';
import Dashboard from './Dashboard';
import { Toaster } from '@/components/ui/toaster';

// Mock the useDashboardDataQuery hook
jest.mock('@/hooks/useDashboardDataQuery', () => ({
  useDashboardDataQuery: () => ({
    properties: [],
    maintenanceTaskResult: { tasks: [], refreshTasks: jest.fn() },
    inventoryItems: [],
    damages: [],
    purchaseOrders: [],
    loading: true,
    error: null,
    refreshData: jest.fn(),
  }),
}));

describe('Dashboard', () => {
  test('renders the dashboard with loading state', () => {
    render(
      <MemoryRouter>
        <AuthContext.Provider value={{ authState: { user: { id: '123' }, isAuthenticated: true } }}>
          <Dashboard />
          <Toaster />
        </AuthContext.Provider>
      </MemoryRouter>
    );

    expect(screen.getByText(/Dashboard/i)).toBeInTheDocument();
    expect(screen.getByText(/Loading.../i)).toBeInTheDocument(); // Assuming DashboardView shows a loading message
  });
});