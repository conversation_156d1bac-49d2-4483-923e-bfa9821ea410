import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter } from 'react-router-dom';
import { AuthContext } from '@/contexts/AuthContext';
import Auth from './Auth';
import { Toaster } from '@/components/ui/toaster';

// Mock the useAuth hook
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    signIn: jest.fn(),
    signUp: jest.fn(),
  }),
}));

describe('Auth', () => {
  test('renders the login and register tabs', () => {
    render(
      <MemoryRouter>
        <Auth />
        <Toaster />
      </MemoryRouter>
    );

    expect(screen.getByText(/Login/i)).toBeInTheDocument();
    expect(screen.getByText(/Register/i)).toBeInTheDocument();
  });

  test('shows the login form by default', () => {
    render(
      <MemoryRouter>
        <Auth />
        <Toaster />
      </MemoryRouter>
    );

    expect(screen.getByLabelText(/Email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Password/i)).toBeInTheDocument();
  });

  test('switches to the register form when the register tab is clicked', () => {
    render(
      <MemoryRouter>
        <Auth />
        <Toaster />
      </MemoryRouter>
    );

    fireEvent.click(screen.getByText(/Register/i));

    expect(screen.getByLabelText(/First Name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Last Name/i)).toBeInTheDocument();
  });
});
